"""
机器狗运动控制API端点
处理基本运动和预定义动作
"""
import logging
import sys
import os
from fastapi import APIRouter, HTTPException, Depends
from typing import Dict, Any

# 添加python目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "../../../python"))

from go2_webrtc import ROBOT_CMD
from app.models.schemas import (
    MovementRequest, ActionRequest, CommandResponse, 
    CommandListResponse, AvailableCommand, BaseResponse
)
from app.services.connection_manager import get_connection_manager, Go2ConnectionManager

logger = logging.getLogger(__name__)
router = APIRouter()


@router.post("/movement", response_model=CommandResponse)
async def control_movement(
    request: MovementRequest,
    manager: Go2ConnectionManager = Depends(get_connection_manager)
):
    """
    控制机器狗基本运动
    
    - **x**: 前后移动速度 (-1.0 到 1.0)，正值向前，负值向后
    - **y**: 左右移动速度 (-1.0 到 1.0)，正值向左，负值向右  
    - **z**: 旋转速度 (-2.0 到 2.0)，正值逆时针，负值顺时针
    """
    if not manager.is_connected:
        raise HTTPException(
            status_code=400,
            detail="机器狗未连接，请先建立连接"
        )
    
    try:
        logger.info(f"发送运动命令: x={request.x}, y={request.y}, z={request.z}")
        
        # 构建运动参数
        movement_params = {
            "x": request.x,
            "y": request.y, 
            "z": request.z
        }
        
        # 发送Move命令 (ID: 1008)
        success = await manager.send_command("Move", movement_params)
        
        if success:
            return CommandResponse(
                success=True,
                message="运动命令发送成功",
                command_sent=True,
                command_name="Move",
                command_id=ROBOT_CMD["Move"]
            )
        else:
            raise HTTPException(
                status_code=500,
                detail="运动命令发送失败"
            )
            
    except Exception as e:
        logger.error(f"控制运动时出错: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"运动控制失败: {str(e)}"
        )


@router.post("/action", response_model=CommandResponse)
async def execute_action(
    request: ActionRequest,
    manager: Go2ConnectionManager = Depends(get_connection_manager)
):
    """
    执行预定义动作
    
    - **action**: 动作名称，如 "StandUp", "Sit", "Hello" 等
    - **parameters**: 可选的动作参数
    """
    if not manager.is_connected:
        raise HTTPException(
            status_code=400,
            detail="机器狗未连接，请先建立连接"
        )
    
    # 验证动作名称
    if request.action not in ROBOT_CMD:
        available_actions = list(ROBOT_CMD.keys())
        raise HTTPException(
            status_code=400,
            detail=f"未知动作: {request.action}。可用动作: {available_actions}"
        )
    
    try:
        logger.info(f"执行动作: {request.action}")
        
        # 发送动作命令
        success = await manager.send_command(request.action, request.parameters)
        
        if success:
            return CommandResponse(
                success=True,
                message=f"动作 '{request.action}' 执行成功",
                command_sent=True,
                command_name=request.action,
                command_id=ROBOT_CMD[request.action]
            )
        else:
            raise HTTPException(
                status_code=500,
                detail=f"动作 '{request.action}' 执行失败"
            )
            
    except Exception as e:
        logger.error(f"执行动作时出错: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"动作执行失败: {str(e)}"
        )


@router.post("/stop", response_model=CommandResponse)
async def stop_movement(
    manager: Go2ConnectionManager = Depends(get_connection_manager)
):
    """
    停止机器狗运动
    
    发送停止运动命令，机器狗将停止当前运动
    """
    if not manager.is_connected:
        raise HTTPException(
            status_code=400,
            detail="机器狗未连接，请先建立连接"
        )
    
    try:
        logger.info("停止机器狗运动")
        
        # 发送StopMove命令
        success = await manager.send_command("StopMove")
        
        if success:
            return CommandResponse(
                success=True,
                message="停止命令发送成功",
                command_sent=True,
                command_name="StopMove",
                command_id=ROBOT_CMD["StopMove"]
            )
        else:
            raise HTTPException(
                status_code=500,
                detail="停止命令发送失败"
            )
            
    except Exception as e:
        logger.error(f"停止运动时出错: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"停止运动失败: {str(e)}"
        )


@router.get("/commands", response_model=CommandListResponse)
async def get_available_commands():
    """
    获取所有可用的机器狗控制命令
    
    返回完整的命令列表，包括命令名称、ID和描述
    """
    try:
        # 命令描述映射
        command_descriptions = {
            "Damp": "阻尼模式",
            "BalanceStand": "平衡站立",
            "StopMove": "停止运动",
            "StandUp": "站起",
            "StandDown": "趴下",
            "RecoveryStand": "恢复站立",
            "Euler": "欧拉角控制",
            "Move": "基本运动",
            "Sit": "坐下",
            "RiseSit": "起立坐下",
            "SwitchGait": "切换步态",
            "Trigger": "触发",
            "BodyHeight": "身体高度",
            "FootRaiseHeight": "抬脚高度",
            "SpeedLevel": "速度等级",
            "Hello": "打招呼",
            "Stretch": "伸展",
            "TrajectoryFollow": "轨迹跟随",
            "ContinuousGait": "连续步态",
            "Content": "内容",
            "Wallow": "打滚",
            "Dance1": "舞蹈1",
            "Dance2": "舞蹈2",
            "GetBodyHeight": "获取身体高度",
            "GetFootRaiseHeight": "获取抬脚高度",
            "GetSpeedLevel": "获取速度等级",
            "SwitchJoystick": "切换手柄",
            "Pose": "姿势",
            "Scrape": "刮擦",
            "FrontFlip": "前空翻",
            "FrontJump": "前跳",
            "FrontPounce": "前扑",
            "WiggleHips": "扭臀",
            "GetState": "获取状态",
            "EconomicGait": "经济步态",
            "FingerHeart": "比心"
        }
        
        commands = []
        for name, cmd_id in ROBOT_CMD.items():
            commands.append(AvailableCommand(
                name=name,
                id=cmd_id,
                description=command_descriptions.get(name, "未知命令"),
                parameters={"x": "float", "y": "float", "z": "float"} if name == "Move" else None
            ))
        
        return CommandListResponse(
            success=True,
            message=f"获取到 {len(commands)} 个可用命令",
            commands=commands
        )
        
    except Exception as e:
        logger.error(f"获取命令列表时出错: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"获取命令列表失败: {str(e)}"
        )


@router.post("/emergency_stop", response_model=CommandResponse)
async def emergency_stop(
    manager: Go2ConnectionManager = Depends(get_connection_manager)
):
    """
    紧急停止
    
    立即停止机器狗所有运动，进入阻尼模式
    """
    if not manager.is_connected:
        raise HTTPException(
            status_code=400,
            detail="机器狗未连接，请先建立连接"
        )
    
    try:
        logger.warning("执行紧急停止")
        
        # 发送Damp命令（阻尼模式）
        success = await manager.send_command("Damp")
        
        if success:
            return CommandResponse(
                success=True,
                message="紧急停止执行成功",
                command_sent=True,
                command_name="Damp",
                command_id=ROBOT_CMD["Damp"]
            )
        else:
            raise HTTPException(
                status_code=500,
                detail="紧急停止执行失败"
            )
            
    except Exception as e:
        logger.error(f"紧急停止时出错: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"紧急停止失败: {str(e)}"
        )
