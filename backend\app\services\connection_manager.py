"""
Go2连接管理器
封装WebRTC连接逻辑，提供单例服务
"""
import asyncio
import json
import logging
import sys
import os
from typing import Optional, Dict, Any, Callable
from datetime import datetime

# 添加python目录到路径以导入go2_webrtc
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "../../../python"))

from go2_webrtc import Go2Connection, ROBOT_CMD, RTC_TOPIC, DATA_CHANNEL_TYPE
from app.core.config import get_settings

logger = logging.getLogger(__name__)


class Go2ConnectionManager:
    """Go2机器狗连接管理器"""
    
    def __init__(self):
        self.settings = get_settings()
        self._connection: Optional[Go2Connection] = None
        self._is_connected = False
        self._connection_lock = asyncio.Lock()
        self._status_cache: Dict[str, Any] = {}
        self._message_callbacks: list[Callable] = []
        self._last_heartbeat = None
        
    @property
    def is_connected(self) -> bool:
        """检查连接状态"""
        return self._is_connected and self._connection is not None
    
    async def connect(self, robot_ip: Optional[str] = None, token: Optional[str] = None) -> bool:
        """连接到机器狗"""
        async with self._connection_lock:
            if self.is_connected:
                logger.info("机器狗已经连接")
                return True
                
            try:
                # 使用提供的参数或配置文件中的默认值
                ip = robot_ip or self.settings.go2_ip
                auth_token = token or self.settings.go2_token
                
                logger.info(f"正在连接到机器狗 {ip}...")
                
                # 创建连接实例
                self._connection = Go2Connection(
                    ip=ip,
                    token=auth_token,
                    on_validated=self._on_validated,
                    on_message=self._on_message,
                    on_open=self._on_open
                )
                
                # 建立WebRTC连接
                await self._connection.connect_robot()
                
                # 等待连接验证
                retry_count = 0
                while (self._connection.validation_result == "PENDING" and 
                       retry_count < self.settings.robot_connection_retry):
                    await asyncio.sleep(1)
                    retry_count += 1
                
                if self._connection.validation_result == "SUCCESS":
                    self._is_connected = True
                    self._last_heartbeat = datetime.now()
                    logger.info("机器狗连接成功")
                    return True
                else:
                    logger.error("机器狗连接验证失败")
                    await self.disconnect()
                    return False
                    
            except Exception as e:
                logger.error(f"连接机器狗失败: {e}")
                await self.disconnect()
                return False
    
    async def disconnect(self) -> bool:
        """断开机器狗连接"""
        async with self._connection_lock:
            try:
                if self._connection:
                    await self._connection.pc.close()
                    self._connection = None
                
                self._is_connected = False
                self._status_cache.clear()
                logger.info("机器狗连接已断开")
                return True
                
            except Exception as e:
                logger.error(f"断开连接时出错: {e}")
                return False
    
    def _on_validated(self):
        """连接验证成功回调"""
        logger.info("机器狗连接验证成功")
        # 订阅所有状态主题
        for topic in RTC_TOPIC.values():
            self._subscribe_topic(topic)
    
    def _on_open(self):
        """数据通道打开回调"""
        logger.info("数据通道已打开")
    
    def _on_message(self, message, msgobj):
        """接收消息回调"""
        try:
            # 更新状态缓存
            if msgobj and isinstance(msgobj, dict):
                topic = msgobj.get("topic", "")
                if topic:
                    self._status_cache[topic] = {
                        "data": msgobj.get("data", {}),
                        "timestamp": datetime.now().isoformat(),
                        "type": msgobj.get("type", "")
                    }
            
            # 调用注册的回调函数
            for callback in self._message_callbacks:
                try:
                    callback(message, msgobj)
                except Exception as e:
                    logger.error(f"消息回调执行失败: {e}")
                    
        except Exception as e:
            logger.error(f"处理消息时出错: {e}")
    
    def _subscribe_topic(self, topic: str):
        """订阅RTC主题"""
        if self._connection and self._connection.data_channel:
            try:
                subscribe_msg = {
                    "type": DATA_CHANNEL_TYPE["SUBSCRIBE"],
                    "topic": topic
                }
                self._connection.data_channel.send(json.dumps(subscribe_msg))
                logger.debug(f"已订阅主题: {topic}")
            except Exception as e:
                logger.error(f"订阅主题失败 {topic}: {e}")
    
    async def send_command(self, command_name: str, parameters: Optional[Dict] = None) -> bool:
        """发送控制命令"""
        if not self.is_connected:
            logger.error("机器狗未连接")
            return False
        
        try:
            # 获取命令ID
            cmd_id = ROBOT_CMD.get(command_name)
            if cmd_id is None:
                logger.error(f"未知命令: {command_name}")
                return False
            
            # 生成唯一ID
            unique_id = Go2Connection.generate_id()
            
            # 构建命令消息
            command_msg = {
                "type": DATA_CHANNEL_TYPE["MSG"],
                "topic": RTC_TOPIC["SPORT_MOD"],
                "data": {
                    "header": {
                        "identity": {
                            "id": unique_id,
                            "api_id": cmd_id
                        }
                    },
                    "parameter": json.dumps(parameters or cmd_id)
                }
            }
            
            # 发送命令
            self._connection.data_channel.send(json.dumps(command_msg))
            logger.info(f"已发送命令: {command_name} (ID: {cmd_id})")
            return True
            
        except Exception as e:
            logger.error(f"发送命令失败: {e}")
            return False
    
    def get_status(self) -> Dict[str, Any]:
        """获取机器狗状态"""
        return {
            "connected": self.is_connected,
            "last_heartbeat": self._last_heartbeat.isoformat() if self._last_heartbeat else None,
            "cached_data": self._status_cache,
            "connection_info": {
                "ip": self.settings.go2_ip,
                "validation_result": self._connection.validation_result if self._connection else "DISCONNECTED"
            }
        }
    
    def add_message_callback(self, callback: Callable):
        """添加消息回调函数"""
        self._message_callbacks.append(callback)
    
    def remove_message_callback(self, callback: Callable):
        """移除消息回调函数"""
        if callback in self._message_callbacks:
            self._message_callbacks.remove(callback)


# 全局连接管理器实例
connection_manager = Go2ConnectionManager()


def get_connection_manager() -> Go2ConnectionManager:
    """获取连接管理器实例"""
    return connection_manager
