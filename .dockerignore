# Go2 智能农业后端API - Docker构建忽略文件 (项目根目录)
# 优化Docker构建性能，减少镜像大小

# ================================
# Python相关
# ================================
**/__pycache__/
**/*.py[cod]
**/*$py.class
**/*.so
**/.Python
**/build/
**/develop-eggs/
**/dist/
**/downloads/
**/eggs/
**/.eggs/
**/lib/
**/lib64/
**/parts/
**/sdist/
**/var/
**/wheels/
**/*.egg-info/
**/.installed.cfg
**/*.egg
**/MANIFEST

# ================================
# 虚拟环境
# ================================
**/venv/
**/env/
**/ENV/
**/env.bak/
**/venv.bak/
**/.venv/

# ================================
# IDE和编辑器
# ================================
.vscode/
.idea/
**/*.swp
**/*.swo
**/*~
.DS_Store
Thumbs.db

# ================================
# 版本控制
# ================================
.git/
.gitignore
.gitattributes

# ================================
# 测试和覆盖率
# ================================
**/.pytest_cache/
**/.coverage
**/.coverage.*
**/coverage.xml
**/*.cover
**/.hypothesis/
**/htmlcov/
**/.tox/

# ================================
# 日志和数据文件
# ================================
**/logs/
**/*.log
**/data/
**/uploads/
**/*.db
**/*.sqlite
**/*.sqlite3

# ================================
# 环境配置文件
# ================================
**/.env
**/.env.local
**/.env.dev
**/.env.prod
**/.env.test

# ================================
# Docker相关
# ================================
**/Dockerfile*
**/docker-compose*.yml
**/.dockerignore

# ================================
# 文档和说明 (排除其他txt但保留requirements.txt)
# ================================
**/README*.md
**/CHANGELOG.md
**/LICENSE
**/*.txt
!**/requirements.txt

# ================================
# JavaScript相关 (不需要在Python容器中)
# ================================
javascript/
**/node_modules/
**/*.js.map
**/package*.json

# ================================
# 临时文件
# ================================
**/*.tmp
**/*.temp
**/.cache/
**/.mypy_cache/
**/.dmypy.json
**/dmypy.json

# ================================
# 操作系统文件
# ================================
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# ================================
# 其他不需要的目录
# ================================
ss/
