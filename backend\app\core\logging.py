"""
日志配置和管理
提供结构化日志记录和错误处理
"""
import logging
import logging.handlers
import json
import sys
import os
from datetime import datetime
from typing import Dict, Any, Optional
from pathlib import Path

from app.core.config import get_settings


class JSONFormatter(logging.Formatter):
    """JSON格式化器"""
    
    def format(self, record: logging.LogRecord) -> str:
        """格式化日志记录为JSON"""
        log_entry = {
            "timestamp": datetime.fromtimestamp(record.created).isoformat(),
            "level": record.levelname,
            "logger": record.name,
            "message": record.getMessage(),
            "module": record.module,
            "function": record.funcName,
            "line": record.lineno
        }
        
        # 添加异常信息
        if record.exc_info:
            log_entry["exception"] = self.formatException(record.exc_info)
        
        # 添加额外字段
        if hasattr(record, 'extra_fields'):
            log_entry.update(record.extra_fields)
        
        return json.dumps(log_entry, ensure_ascii=False)


class StructuredLogger:
    """结构化日志记录器"""
    
    def __init__(self, name: str):
        self.logger = logging.getLogger(name)
        self._setup_logger()
    
    def _setup_logger(self):
        """设置日志记录器"""
        settings = get_settings()
        
        # 设置日志级别
        level = getattr(logging, settings.log_level.upper(), logging.INFO)
        self.logger.setLevel(level)
        
        # 避免重复添加处理器
        if self.logger.handlers:
            return
        
        # 控制台处理器
        console_handler = logging.StreamHandler(sys.stdout)
        
        if settings.log_format.lower() == "json":
            console_handler.setFormatter(JSONFormatter())
        else:
            console_handler.setFormatter(
                logging.Formatter(
                    '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
                )
            )
        
        self.logger.addHandler(console_handler)
        
        # 文件处理器（如果配置了日志目录）
        log_dir = os.getenv("LOG_DIR")
        if log_dir:
            self._setup_file_handler(log_dir, settings)
    
    def _setup_file_handler(self, log_dir: str, settings):
        """设置文件处理器"""
        try:
            log_path = Path(log_dir)
            log_path.mkdir(parents=True, exist_ok=True)
            
            # 应用日志文件
            app_log_file = log_path / "go2_api.log"
            file_handler = logging.handlers.RotatingFileHandler(
                app_log_file,
                maxBytes=10 * 1024 * 1024,  # 10MB
                backupCount=5
            )
            
            if settings.log_format.lower() == "json":
                file_handler.setFormatter(JSONFormatter())
            else:
                file_handler.setFormatter(
                    logging.Formatter(
                        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
                    )
                )
            
            self.logger.addHandler(file_handler)
            
            # 错误日志文件
            error_log_file = log_path / "go2_api_errors.log"
            error_handler = logging.handlers.RotatingFileHandler(
                error_log_file,
                maxBytes=10 * 1024 * 1024,  # 10MB
                backupCount=5
            )
            error_handler.setLevel(logging.ERROR)
            error_handler.setFormatter(JSONFormatter())
            
            self.logger.addHandler(error_handler)
            
        except Exception as e:
            self.logger.error(f"设置文件日志处理器失败: {e}")
    
    def info(self, message: str, **kwargs):
        """记录信息日志"""
        self._log(logging.INFO, message, **kwargs)
    
    def debug(self, message: str, **kwargs):
        """记录调试日志"""
        self._log(logging.DEBUG, message, **kwargs)
    
    def warning(self, message: str, **kwargs):
        """记录警告日志"""
        self._log(logging.WARNING, message, **kwargs)
    
    def error(self, message: str, **kwargs):
        """记录错误日志"""
        self._log(logging.ERROR, message, **kwargs)
    
    def critical(self, message: str, **kwargs):
        """记录严重错误日志"""
        self._log(logging.CRITICAL, message, **kwargs)
    
    def _log(self, level: int, message: str, **kwargs):
        """内部日志记录方法"""
        extra = {"extra_fields": kwargs} if kwargs else {}
        self.logger.log(level, message, extra=extra)


class ErrorHandler:
    """错误处理器"""
    
    def __init__(self):
        self.logger = StructuredLogger("error_handler")
    
    def handle_connection_error(self, error: Exception, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """处理连接错误"""
        error_info = {
            "error_type": "connection_error",
            "error_message": str(error),
            "error_class": error.__class__.__name__,
            "context": context or {}
        }
        
        self.logger.error("机器狗连接错误", **error_info)
        
        return {
            "success": False,
            "error_code": "CONNECTION_FAILED",
            "message": "机器狗连接失败",
            "details": error_info
        }
    
    def handle_command_error(self, error: Exception, command: str, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """处理命令错误"""
        error_info = {
            "error_type": "command_error",
            "error_message": str(error),
            "error_class": error.__class__.__name__,
            "command": command,
            "context": context or {}
        }
        
        self.logger.error("命令执行错误", **error_info)
        
        return {
            "success": False,
            "error_code": "COMMAND_FAILED",
            "message": f"命令 '{command}' 执行失败",
            "details": error_info
        }
    
    def handle_websocket_error(self, error: Exception, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """处理WebSocket错误"""
        error_info = {
            "error_type": "websocket_error",
            "error_message": str(error),
            "error_class": error.__class__.__name__,
            "context": context or {}
        }
        
        self.logger.error("WebSocket错误", **error_info)
        
        return {
            "success": False,
            "error_code": "WEBSOCKET_ERROR",
            "message": "WebSocket通信错误",
            "details": error_info
        }
    
    def handle_validation_error(self, error: Exception, data: Dict[str, Any] = None) -> Dict[str, Any]:
        """处理数据验证错误"""
        error_info = {
            "error_type": "validation_error",
            "error_message": str(error),
            "error_class": error.__class__.__name__,
            "invalid_data": data or {}
        }
        
        self.logger.warning("数据验证错误", **error_info)
        
        return {
            "success": False,
            "error_code": "VALIDATION_ERROR",
            "message": "请求数据验证失败",
            "details": error_info
        }
    
    def handle_generic_error(self, error: Exception, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """处理通用错误"""
        error_info = {
            "error_type": "generic_error",
            "error_message": str(error),
            "error_class": error.__class__.__name__,
            "context": context or {}
        }
        
        self.logger.error("通用错误", **error_info)
        
        return {
            "success": False,
            "error_code": "INTERNAL_ERROR",
            "message": "内部服务器错误",
            "details": error_info
        }


# 全局实例
error_handler = ErrorHandler()


def get_logger(name: str) -> StructuredLogger:
    """获取结构化日志记录器"""
    return StructuredLogger(name)


def get_error_handler() -> ErrorHandler:
    """获取错误处理器"""
    return error_handler


def setup_logging():
    """设置全局日志配置"""
    settings = get_settings()
    
    # 设置根日志级别
    root_logger = logging.getLogger()
    level = getattr(logging, settings.log_level.upper(), logging.INFO)
    root_logger.setLevel(level)
    
    # 禁用一些第三方库的详细日志
    logging.getLogger("aiortc").setLevel(logging.WARNING)
    logging.getLogger("aiohttp").setLevel(logging.WARNING)
    logging.getLogger("websockets").setLevel(logging.WARNING)
    
    logger = get_logger("setup")
    logger.info("日志系统初始化完成", log_level=settings.log_level, log_format=settings.log_format)
