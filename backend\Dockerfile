# Go2 智能农业后端API - 开发环境Docker配置
# 基于FastAPI + Python 3.12，专为开发环境优化

# ================================
# 第一阶段：基础环境准备
# ================================
FROM python:3.12-slim AS base

# 设置环境变量
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1

# 设置工作目录
WORKDIR /app

# 安装系统依赖
# gcc: 编译aiortc等包需要
# curl: 健康检查需要
# procps: 进程管理工具
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    curl \
    procps \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# ================================
# 第二阶段：依赖安装 (缓存优化)
# ================================
FROM base AS dependencies

# 复制依赖文件
COPY backend/requirements.txt .

# 复制go2_webrtc模块
COPY python/ ./python/

# 调试：检查文件是否正确复制
RUN ls -la ./python/ && ls -la ./python/go2_webrtc/

# 安装Python依赖
RUN pip install --no-cache-dir --upgrade pip && \
    pip install --no-cache-dir -r requirements.txt && \
    pip install --no-cache-dir -e ./python/

# 调试：验证go2_webrtc模块安装
RUN python -c "import go2_webrtc; print('go2_webrtc模块安装成功!')" || echo "go2_webrtc模块安装失败"

# ================================
# 第三阶段：应用环境
# ================================
FROM dependencies AS application

# 复制应用代码
COPY backend/ .

# 确保python目录在正确位置 (为了兼容现有的sys.path逻辑)
RUN ln -sf /app/python /app/../python

# 创建必要的目录
RUN mkdir -p /app/data /app/logs /app/uploads

# 创建非root用户 (安全最佳实践)
RUN groupadd -r appgroup && \
    useradd -r -g appgroup -u 1000 -d /app -s /bin/bash appuser && \
    chown -R appuser:appgroup /app

# 切换到非root用户
USER appuser

# 设置Python路径
ENV PYTHONPATH=/app

# 暴露端口
# 8000: FastAPI应用端口
# 5678: debugpy调试端口 (可选)
EXPOSE 8000 5678

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=40s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# 默认启动命令 (开发模式)
CMD ["python", "start_server.py", "--host", "0.0.0.0", "--port", "8000", "--reload", "--log-level", "DEBUG"]
