# Go2机器狗连接配置
GO2_IP=*************
GO2_TOKEN=

# API服务器配置
API_HOST=0.0.0.0
API_PORT=8000
API_RELOAD=true

# 日志配置
LOG_LEVEL=INFO
LOG_FORMAT=json

# 数据库配置
DATABASE_URL=sqlite:///./go2_api.db

# 安全配置
SECRET_KEY=your-secret-key-here
ACCESS_TOKEN_EXPIRE_MINUTES=30

# WebSocket配置
WS_HEARTBEAT_INTERVAL=30
WS_MAX_CONNECTIONS=100

# 机器狗控制配置
ROBOT_COMMAND_TIMEOUT=5.0
ROBOT_CONNECTION_RETRY=3
ROBOT_HEARTBEAT_INTERVAL=10.0
