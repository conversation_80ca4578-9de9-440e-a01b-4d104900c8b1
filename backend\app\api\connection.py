"""
机器狗连接管理API端点
处理连接、断开和状态查询
"""
import logging
from fastapi import APIRouter, HTTPException, Depends
from typing import Dict, Any

from app.models.schemas import (
    ConnectionRequest, ConnectionResponse, ConnectionStatus, 
    BaseResponse, ErrorResponse, SystemInfo, ApiConfig
)
from app.services.connection_manager import get_connection_manager, Go2ConnectionManager
from app.core.config import get_settings

logger = logging.getLogger(__name__)
router = APIRouter()


@router.post(
    "/connect",
    response_model=ConnectionResponse,
    summary="连接机器狗",
    description="建立与Unitree Go2机器狗的WebRTC连接",
    responses={
        200: {
            "description": "连接成功",
            "content": {
                "application/json": {
                    "example": {
                        "success": True,
                        "message": "机器狗连接成功",
                        "timestamp": "2024-12-27T16:00:00",
                        "connected": True,
                        "robot_ip": "*************"
                    }
                }
            }
        },
        400: {
            "description": "连接失败",
            "content": {
                "application/json": {
                    "example": {
                        "detail": "机器狗连接失败，请检查IP地址和网络连接"
                    }
                }
            }
        },
        500: {"description": "服务器内部错误"}
    },
    tags=["连接管理"]
)
async def connect_robot(
    request: ConnectionRequest,
    manager: Go2ConnectionManager = Depends(get_connection_manager)
):
    """
    ## 连接到机器狗

    建立与Unitree Go2机器狗的WebRTC连接。连接成功后，可以通过其他API端点控制机器狗。

    ### 参数说明
    - **robot_ip**: 机器狗的IP地址（必填）
        - 格式：IPv4地址，如 "*************"
        - 确保机器狗与服务器在同一网络中
    - **token**: 认证令牌（可选）
        - 用于多客户端同时连接
        - 可通过机器狗官方APP或API获取

    ### 使用示例
    ```bash
    curl -X POST "http://localhost:8000/api/connect" \\
      -H "Content-Type: application/json" \\
      -d '{
        "robot_ip": "*************",
        "token": "your_token_here"
      }'
    ```

    ### 注意事项
    - 连接前请确保机器狗已开机并连接到网络
    - 同一时间只能有一个活跃连接
    - 连接超时时间为10秒
    """
    try:
        logger.info(f"尝试连接到机器狗: {request.robot_ip}")
        
        success = await manager.connect(
            robot_ip=request.robot_ip,
            token=request.token
        )
        
        if success:
            return ConnectionResponse(
                success=True,
                message="机器狗连接成功",
                connected=True,
                robot_ip=request.robot_ip
            )
        else:
            raise HTTPException(
                status_code=400,
                detail="机器狗连接失败，请检查IP地址和网络连接"
            )
            
    except Exception as e:
        logger.error(f"连接机器狗时出错: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"连接失败: {str(e)}"
        )


@router.delete("/disconnect", response_model=BaseResponse)
async def disconnect_robot(
    manager: Go2ConnectionManager = Depends(get_connection_manager)
):
    """
    断开机器狗连接
    """
    try:
        logger.info("断开机器狗连接")
        
        success = await manager.disconnect()
        
        if success:
            return BaseResponse(
                success=True,
                message="机器狗连接已断开"
            )
        else:
            return BaseResponse(
                success=False,
                message="断开连接时出现问题"
            )
            
    except Exception as e:
        logger.error(f"断开连接时出错: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"断开连接失败: {str(e)}"
        )


@router.get("/status", response_model=ConnectionStatus)
async def get_connection_status(
    manager: Go2ConnectionManager = Depends(get_connection_manager)
):
    """
    获取机器狗连接状态
    
    返回详细的连接状态信息，包括：
    - 连接状态
    - 最后心跳时间
    - 缓存的状态数据
    - 连接信息
    """
    try:
        status = manager.get_status()
        
        return ConnectionStatus(
            connected=status["connected"],
            last_heartbeat=status["last_heartbeat"],
            connection_info=status["connection_info"],
            cached_data=status["cached_data"]
        )
        
    except Exception as e:
        logger.error(f"获取连接状态时出错: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"获取状态失败: {str(e)}"
        )


@router.get("/info", response_model=SystemInfo)
async def get_system_info(
    manager: Go2ConnectionManager = Depends(get_connection_manager)
):
    """
    获取系统信息
    
    返回API版本、服务器时间等系统信息
    """
    try:
        return SystemInfo(
            api_version="1.0.0",
            connections=1 if manager.is_connected else 0
        )
        
    except Exception as e:
        logger.error(f"获取系统信息时出错: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"获取系统信息失败: {str(e)}"
        )


@router.get("/config", response_model=ApiConfig)
async def get_api_config():
    """
    获取API配置信息
    
    返回当前的API配置参数
    """
    try:
        settings = get_settings()
        
        return ApiConfig(
            robot_ip=settings.go2_ip,
            heartbeat_interval=settings.robot_heartbeat_interval,
            command_timeout=settings.robot_command_timeout,
            max_connections=settings.ws_max_connections
        )
        
    except Exception as e:
        logger.error(f"获取配置信息时出错: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"获取配置失败: {str(e)}"
        )


@router.post("/reconnect", response_model=ConnectionResponse)
async def reconnect_robot(
    manager: Go2ConnectionManager = Depends(get_connection_manager)
):
    """
    重新连接机器狗
    
    使用当前配置重新建立连接
    """
    try:
        logger.info("重新连接机器狗")
        
        # 先断开现有连接
        if manager.is_connected:
            await manager.disconnect()
        
        # 重新连接
        success = await manager.connect()
        
        if success:
            return ConnectionResponse(
                success=True,
                message="机器狗重新连接成功",
                connected=True,
                robot_ip=get_settings().go2_ip
            )
        else:
            raise HTTPException(
                status_code=400,
                detail="机器狗重新连接失败"
            )
            
    except Exception as e:
        logger.error(f"重新连接时出错: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"重新连接失败: {str(e)}"
        )
