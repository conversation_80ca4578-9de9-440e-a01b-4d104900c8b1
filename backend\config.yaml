# Go2 WebRTC API 配置文件
# 可以通过环境变量覆盖这些设置

# 机器狗连接配置
robot:
  ip: "************"  # 机器狗IP地址
  token: "eyJkYXRhMSI6Ik9rOENIbDdrOGZNSUlCSURBTkJna3Foa2lHOXcwQkFRRUZBQU9DQVEwQU1JSUJDQUtDQVFFQXM1Q1ptZkdGVmhKYVlWMWZXS0V6dmFpYlpzUndKVi9QSkhteHd1QVZhNE9uaTZWbCt4bmc0L1dVQk5CVGhiMUNjZUtMMHEzN0NkZ2doOUNtYS9YNjYwNldJTWdOUmlpVmNrZFMzZUNQTzBUS2NuQUE0eE1reWtzeVBBaGlrejhoaWEvQ1BvLy9vcVdPVmlmZnM4K09uWjlHY1VLZVA3S2hvVU0yVENtRy9pTjB5Nm9RNGxIRURnc3dlMUJ5cVRFbEtHQldqcFdyeFRHaXhzYXZMQVZuZmhTSldoMmlFU0k0S0tHVTN5bm1qK3Q3MUt1NFFaQlA0bmNmWGdNTXZ5bWRxUHhQc2h6REZIbWhJUXNHVnllOXZYSmxOMzNVTVpWcTB2MEU2WGFaSlZvVXRHK3hJUjRROXhGelVyWVpUMkswSmh2cGlHSHFQcm90NFJmSGhTVkprUUlCRVE9PUNHQkdFSlFDN0YifQ=="            # 认证令牌（可选）
  connection_timeout: 10.0    # 连接超时时间（秒）
  command_timeout: 5.0        # 命令超时时间（秒）
  heartbeat_interval: 10.0    # 心跳间隔（秒）
  max_retry_attempts: 3       # 最大重试次数

# API服务器配置
server:
  host: "0.0.0.0"      # 监听地址
  port: 8000           # 监听端口
  reload: false        # 开发模式自动重载
  workers: 1           # 工作进程数
  
# 日志配置
logging:
  level: "INFO"        # 日志级别: DEBUG, INFO, WARNING, ERROR, CRITICAL
  format: "json"       # 日志格式: json, text
  file_enabled: false  # 是否启用文件日志
  file_path: "./logs"  # 日志文件路径
  max_file_size: 10    # 最大文件大小（MB）
  backup_count: 5      # 备份文件数量

# WebSocket配置
websocket:
  heartbeat_interval: 30    # 心跳间隔（秒）
  max_connections: 100      # 最大连接数
  message_queue_size: 1000  # 消息队列大小

# 状态管理配置
state:
  cache_max_history: 100    # 最大历史记录数
  cache_ttl_seconds: 300    # 缓存过期时间（秒）
  cleanup_interval: 60      # 清理间隔（秒）

# 安全配置
security:
  secret_key: "your-secret-key-here"  # JWT密钥
  token_expire_minutes: 30            # 令牌过期时间（分钟）
  cors_origins: ["*"]                 # CORS允许的源
  cors_methods: ["*"]                 # CORS允许的方法
  cors_headers: ["*"]                 # CORS允许的头部

# 数据库配置
database:
  url: "sqlite:///./go2_api.db"  # 数据库URL
  echo: false                    # 是否打印SQL语句
  pool_size: 5                   # 连接池大小
  max_overflow: 10               # 最大溢出连接数

# 性能配置
performance:
  request_timeout: 30.0      # 请求超时时间（秒）
  max_request_size: 16       # 最大请求大小（MB）
  enable_compression: true   # 启用响应压缩
  
# 监控配置
monitoring:
  enable_metrics: true       # 启用指标收集
  metrics_endpoint: "/metrics"  # 指标端点
  health_check_interval: 30  # 健康检查间隔（秒）

# 功能开关
features:
  enable_video_stream: false    # 启用视频流
  enable_lidar_data: true       # 启用激光雷达数据
  enable_uwb_data: true         # 启用UWB数据
  enable_audio_stream: false    # 启用音频流
  enable_advanced_control: true # 启用高级控制功能
