"""
API数据模型和Schema定义
定义请求和响应的数据结构
"""
from typing import Optional, Dict, Any, List
from datetime import datetime
from pydantic import BaseModel, Field


# 基础响应模型
class BaseResponse(BaseModel):
    """基础响应模型"""
    success: bool = Field(..., description="操作是否成功")
    message: str = Field(..., description="响应消息")
    timestamp: datetime = Field(default_factory=datetime.now, description="响应时间戳")


class ErrorResponse(BaseResponse):
    """错误响应模型"""
    success: bool = Field(default=False, description="操作失败")
    error_code: Optional[str] = Field(None, description="错误代码")
    details: Optional[Dict[str, Any]] = Field(None, description="错误详情")


# 连接管理相关模型
class ConnectionRequest(BaseModel):
    """连接请求模型"""
    robot_ip: str = Field(..., description="机器狗IP地址", example="*************")
    token: Optional[str] = Field("", description="认证令牌")


class ConnectionResponse(BaseResponse):
    """连接响应模型"""
    connected: bool = Field(..., description="连接状态")
    robot_ip: Optional[str] = Field(None, description="机器狗IP地址")


class ConnectionStatus(BaseModel):
    """连接状态模型"""
    connected: bool = Field(..., description="是否已连接")
    last_heartbeat: Optional[str] = Field(None, description="最后心跳时间")
    connection_info: Dict[str, Any] = Field(..., description="连接信息")
    cached_data: Dict[str, Any] = Field(..., description="缓存的状态数据")


# 运动控制相关模型
class MovementRequest(BaseModel):
    """运动控制请求模型"""
    x: float = Field(0.0, description="前后移动速度 (-1.0 到 1.0)", ge=-1.0, le=1.0)
    y: float = Field(0.0, description="左右移动速度 (-1.0 到 1.0)", ge=-1.0, le=1.0)
    z: float = Field(0.0, description="旋转速度 (-2.0 到 2.0)", ge=-2.0, le=2.0)


class ActionRequest(BaseModel):
    """动作请求模型"""
    action: str = Field(..., description="动作名称", example="StandUp")
    parameters: Optional[Dict[str, Any]] = Field(None, description="动作参数")


class CommandResponse(BaseResponse):
    """命令响应模型"""
    command_sent: bool = Field(..., description="命令是否发送成功")
    command_name: Optional[str] = Field(None, description="命令名称")
    command_id: Optional[int] = Field(None, description="命令ID")


# WebSocket消息模型
class WebSocketMessage(BaseModel):
    """WebSocket消息模型"""
    type: str = Field(..., description="消息类型")
    data: Dict[str, Any] = Field(..., description="消息数据")
    timestamp: datetime = Field(default_factory=datetime.now, description="消息时间戳")


class WebSocketCommand(BaseModel):
    """WebSocket命令模型"""
    action: str = Field(..., description="命令动作")
    payload: Optional[Dict[str, Any]] = Field(None, description="命令载荷")


# 机器狗状态相关模型
class RobotState(BaseModel):
    """机器狗状态模型"""
    position: Optional[Dict[str, float]] = Field(None, description="位置信息")
    orientation: Optional[Dict[str, float]] = Field(None, description="姿态信息")
    battery_level: Optional[float] = Field(None, description="电池电量")
    temperature: Optional[float] = Field(None, description="温度")
    status: str = Field("unknown", description="状态描述")


# IMU相关数据模型
class IMUState(BaseModel):
    """IMU状态模型"""
    rpy: List[float] = Field(..., description="Roll, Pitch, Yaw 欧拉角", example=[-0.025589, 0.016141, 3.016217])


class MotorState(BaseModel):
    """电机状态模型"""
    q: float = Field(..., description="关节角度")
    temperature: int = Field(..., description="电机温度")
    lost: int = Field(..., description="丢包数")
    reserve: List[int] = Field(..., description="保留字段")


class BMSState(BaseModel):
    """电池管理系统状态模型"""
    version_high: int = Field(..., description="版本号高位")
    version_low: int = Field(..., description="版本号低位")
    soc: int = Field(..., description="电量百分比")
    current: int = Field(..., description="电流")
    cycle: int = Field(..., description="充电循环次数")
    bq_ntc: List[int] = Field(..., description="BQ NTC温度")
    mcu_ntc: List[int] = Field(..., description="MCU NTC温度")


class LowStateData(BaseModel):
    """低级状态数据模型"""
    imu_state: IMUState = Field(..., description="IMU状态")
    motor_state: List[MotorState] = Field(..., description="电机状态列表")
    bms_state: BMSState = Field(..., description="电池管理系统状态")
    foot_force: List[int] = Field(..., description="足部力传感器数据")
    temperature_ntc1: int = Field(..., description="NTC1温度")
    power_v: float = Field(..., description="电源电压")


class IMUDataResponse(BaseResponse):
    """IMU数据响应模型"""
    data: Optional[LowStateData] = Field(None, description="IMU和状态数据")
    topic: str = Field("rt/lf/lowstate", description="数据主题")
    last_updated: Optional[str] = Field(None, description="最后更新时间")


class SensorData(BaseModel):
    """传感器数据模型"""
    lidar: Optional[Dict[str, Any]] = Field(None, description="激光雷达数据")
    imu: Optional[Dict[str, Any]] = Field(None, description="IMU数据")
    uwb: Optional[Dict[str, Any]] = Field(None, description="UWB数据")
    timestamp: datetime = Field(default_factory=datetime.now, description="数据时间戳")


# 可用命令列表模型
class AvailableCommand(BaseModel):
    """可用命令模型"""
    name: str = Field(..., description="命令名称")
    id: int = Field(..., description="命令ID")
    description: str = Field(..., description="命令描述")
    parameters: Optional[Dict[str, str]] = Field(None, description="参数说明")


class CommandListResponse(BaseResponse):
    """命令列表响应模型"""
    commands: List[AvailableCommand] = Field(..., description="可用命令列表")


# 系统信息模型
class SystemInfo(BaseModel):
    """系统信息模型"""
    api_version: str = Field(..., description="API版本")
    server_time: datetime = Field(default_factory=datetime.now, description="服务器时间")
    uptime: Optional[float] = Field(None, description="运行时间（秒）")
    connections: int = Field(0, description="当前连接数")


# 配置模型
class ApiConfig(BaseModel):
    """API配置模型"""
    robot_ip: str = Field(..., description="默认机器狗IP")
    heartbeat_interval: float = Field(..., description="心跳间隔")
    command_timeout: float = Field(..., description="命令超时时间")
    max_connections: int = Field(..., description="最大连接数")
