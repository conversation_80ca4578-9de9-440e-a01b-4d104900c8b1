# Go2 智能农业后端API - Docker开发环境指南

## 🐳 概述

本文档提供了Go2智能农业后端API项目的Docker开发环境完整配置和使用指南。配置专为开发环境优化，支持热重载、调试和开发工具集成。

## 📋 系统要求

- Docker 20.10+
- Docker Compose 2.0+
- 至少2GB可用内存
- 与Go2机器狗在同一局域网

## 🚀 快速开始

### 1. 环境准备

```bash
# 进入后端目录
cd backend

# 复制开发环境配置
cp .env.dev .env

# 编辑配置文件，设置机器狗IP
nano .env
```

### 2. 构建和启动

```bash
# 构建Docker镜像
docker-compose build

# 启动开发环境
docker-compose up -d

# 查看启动日志
docker-compose logs -f go2-api
```

### 3. 验证服务

```bash
# 检查服务状态
docker-compose ps

# 健康检查
curl http://localhost:8000/health

# 访问API文档
open http://localhost:8000/docs
```

## 🛠️ 开发环境特性

### 核心功能
- ✅ **Python 3.12**: 最新Python版本支持
- ✅ **热重载**: 代码变更自动重启服务
- ✅ **调试支持**: 暴露调试端口5678
- ✅ **数据持久化**: 数据库、日志、上传文件
- ✅ **网络访问**: host模式访问局域网机器狗
- ✅ **健康检查**: 自动监控服务状态

### 访问地址
- **API服务**: http://localhost:8000
- **API文档**: http://localhost:8000/docs
- **ReDoc文档**: http://localhost:8000/redoc
- **健康检查**: http://localhost:8000/health

## 🔧 常用命令

### 基础操作
```bash
# 启动所有服务
docker-compose up -d

# 停止所有服务
docker-compose down

# 重启API服务
docker-compose restart go2-api

# 查看实时日志
docker-compose logs -f

# 进入容器调试
docker-compose exec go2-api bash
```

### 开发操作
```bash
# 重新构建镜像
docker-compose build --no-cache

# 查看容器状态
docker-compose ps

# 清理未使用的资源
docker system prune -f
```

## 🗄️ 可选开发工具

### PostgreSQL数据库 + pgAdmin
```bash
# 启动PostgreSQL和管理工具
docker-compose --profile postgres up -d

# 访问pgAdmin: http://localhost:8080
# 用户名: <EMAIL>
# 密码: admin123
```

### Redis缓存
```bash
# 启动Redis
docker-compose --profile redis up -d
```

## 📁 数据持久化

### 数据卷说明
- `go2_dev_data`: 应用数据 (数据库文件)
- `go2_dev_logs`: 应用日志
- `go2_dev_uploads`: 上传文件
- `postgres_dev_data`: PostgreSQL数据
- `pgadmin_dev_data`: pgAdmin配置

### 数据备份
```bash
# 备份SQLite数据库
docker-compose exec go2-api cp /app/data/go2_api.db /app/backup_$(date +%Y%m%d).db

# 备份PostgreSQL
docker-compose exec postgres pg_dump -U go2_user go2_dev > backup_$(date +%Y%m%d).sql
```

## 🔍 调试指南

### 查看日志
```bash
# API服务日志
docker-compose logs -f go2-api

# 数据库日志
docker-compose logs -f postgres

# 所有服务日志
docker-compose logs -f
```

### 性能监控
```bash
# 查看资源使用
docker stats

# 查看容器详情
docker-compose exec go2-api ps aux
```

## 🚨 故障排除

### 常见问题

**1. 端口占用**
```bash
# 检查端口占用
netstat -tulpn | grep :8000

# 停止占用进程
sudo kill -9 <PID>
```

**2. 权限问题**
```bash
# 修复文件权限
sudo chown -R $USER:$USER .
```

**3. 网络连接问题**
```bash
# 检查机器狗连接
ping *************

# 检查容器网络
docker network ls
```

**4. 构建失败**
```bash
# 清理Docker缓存
docker system prune -a

# 重新构建
docker-compose build --no-cache
```

## ⚙️ 配置说明

### 环境变量
主要配置项在 `.env` 文件中：
- `GO2_IP`: 机器狗IP地址
- `API_PORT`: API服务端口
- `LOG_LEVEL`: 日志级别
- `DATABASE_URL`: 数据库连接

### 网络模式
- **host模式**: 直接访问局域网 (默认)
- **bridge模式**: 隔离网络 (需要端口映射)

## 🔒 安全注意事项

- 开发环境使用非root用户运行
- 敏感信息通过环境变量配置
- 生产环境需要更改默认密钥
- 建议在生产环境使用bridge网络模式

## 📞 技术支持

如遇问题，请检查：
1. Docker和Docker Compose版本
2. 网络连接状态
3. 配置文件格式
4. 日志错误信息

---

**开发愉快！🎉**
