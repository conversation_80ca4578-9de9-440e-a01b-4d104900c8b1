# Go2 IMU API 使用说明

## 概述

本API提供了获取Unitree Go2机器狗IMU（惯性测量单元）和低级状态数据的接口。数据来源于机器狗的`rt/lf/lowstate`主题，包含IMU姿态、电机状态、电池信息等。

## API端点

### 1. 获取完整IMU状态

**端点**: `GET /api/imu/status`

**描述**: 获取机器狗的完整IMU和状态数据

**响应示例**:
```json
{
  "success": true,
  "message": "IMU数据获取成功",
  "timestamp": "2024-01-01T12:00:00",
  "data": {
    "imu_state": {
      "rpy": [-0.025589, 0.016141, 3.016217]
    },
    "motor_state": [
      {
        "q": 0.040807,
        "temperature": 32,
        "lost": 10,
        "reserve": [0, 651]
      }
    ],
    "bms_state": {
      "version_high": 1,
      "version_low": 18,
      "soc": 85,
      "current": -2037,
      "cycle": 9,
      "bq_ntc": [31, 29],
      "mcu_ntc": [34, 32]
    },
    "foot_force": [92, 100, 88, 88],
    "temperature_ntc1": 45,
    "power_v": 30.748035
  },
  "topic": "rt/lf/lowstate",
  "last_updated": "2024-01-01T12:00:00"
}
```

### 2. 获取IMU姿态角

**端点**: `GET /api/imu/rpy`

**描述**: 获取机器狗的Roll、Pitch、Yaw姿态角

**响应示例**:
```json
{
  "success": true,
  "message": "IMU姿态角获取成功",
  "data": {
    "roll": -0.025589,
    "pitch": 0.016141,
    "yaw": 3.016217,
    "rpy_array": [-0.025589, 0.016141, 3.016217]
  },
  "timestamp": "2024-01-01T12:00:00",
  "last_updated": "2024-01-01T12:00:00"
}
```

### 3. 获取电机状态

**端点**: `GET /api/imu/motors`

**描述**: 获取机器狗的电机状态信息

**响应示例**:
```json
{
  "success": true,
  "message": "电机状态获取成功",
  "data": {
    "motor_count": 20,
    "motors": [
      {
        "q": 0.040807,
        "temperature": 32,
        "lost": 10,
        "reserve": [0, 651]
      }
    ],
    "active_motors": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]
  },
  "timestamp": "2024-01-01T12:00:00",
  "last_updated": "2024-01-01T12:00:00"
}
```

### 4. 获取电池状态

**端点**: `GET /api/imu/battery`

**描述**: 获取机器狗的电池状态信息

**响应示例**:
```json
{
  "success": true,
  "message": "电池状态获取成功",
  "data": {
    "soc": 85,
    "voltage": 30.748035,
    "current": -2037,
    "cycle": 9,
    "temperature": {
      "bq_ntc": [31, 29],
      "mcu_ntc": [34, 32]
    },
    "version": {
      "high": 1,
      "low": 18
    }
  },
  "timestamp": "2024-01-01T12:00:00",
  "last_updated": "2024-01-01T12:00:00"
}
```

## 数据字段说明

### IMU状态 (imu_state)
- `rpy`: Roll、Pitch、Yaw欧拉角数组，单位为弧度

### 电机状态 (motor_state)
- `q`: 关节角度
- `temperature`: 电机温度（摄氏度）
- `lost`: 丢包数
- `reserve`: 保留字段

### 电池管理系统 (bms_state)
- `soc`: 电量百分比 (0-100)
- `current`: 电流值
- `cycle`: 充电循环次数
- `bq_ntc`: BQ芯片NTC温度传感器数据
- `mcu_ntc`: MCU NTC温度传感器数据
- `version_high/low`: BMS版本号

### 其他字段
- `foot_force`: 四个足部力传感器数据
- `temperature_ntc1`: NTC1温度传感器数据
- `power_v`: 电源电压

## 使用前提

1. **机器狗连接**: 必须先通过 `/api/connection/connect` 建立与机器狗的WebRTC连接
2. **数据订阅**: 连接成功后，系统会自动订阅 `rt/lf/lowstate` 主题
3. **数据缓存**: API返回的是最近接收到的缓存数据

## 错误处理

### 常见错误码
- `503`: 机器狗未连接
- `500`: 内部服务器错误

### 错误响应示例
```json
{
  "success": false,
  "message": "机器狗未连接，请先建立连接",
  "timestamp": "2024-01-01T12:00:00"
}
```

## 使用示例

### Python示例
```python
import aiohttp
import asyncio

async def get_imu_data():
    async with aiohttp.ClientSession() as session:
        # 获取IMU姿态角
        async with session.get('http://localhost:8000/api/imu/rpy') as resp:
            data = await resp.json()
            if data['success']:
                rpy = data['data']
                print(f"Roll: {rpy['roll']}, Pitch: {rpy['pitch']}, Yaw: {rpy['yaw']}")

asyncio.run(get_imu_data())
```

### JavaScript示例
```javascript
// 获取电池状态
fetch('http://localhost:8000/api/imu/battery')
  .then(response => response.json())
  .then(data => {
    if (data.success) {
      console.log(`电池电量: ${data.data.soc}%`);
      console.log(`电压: ${data.data.voltage}V`);
    }
  });
```

### curl示例
```bash
# 获取完整IMU状态
curl -X GET "http://localhost:8000/api/imu/status"

# 获取姿态角
curl -X GET "http://localhost:8000/api/imu/rpy"

# 获取电机状态
curl -X GET "http://localhost:8000/api/imu/motors"

# 获取电池状态
curl -X GET "http://localhost:8000/api/imu/battery"
```

## 测试

运行测试脚本验证API功能：
```bash
cd backend
python test_imu_api.py
```

## 注意事项

1. **数据实时性**: API返回的是缓存数据，实时性取决于机器狗的数据发送频率
2. **连接状态**: 如果机器狗断开连接，API会返回最后缓存的数据，但`success`字段可能为`false`
3. **数据完整性**: 某些情况下数据可能不完整，请检查响应中的`success`字段
4. **性能**: 频繁调用API不会影响机器狗性能，因为数据来自本地缓存

## API文档

启动服务器后，可以通过以下地址查看完整的API文档：
- Swagger UI: http://localhost:8000/docs
- ReDoc: http://localhost:8000/redoc
