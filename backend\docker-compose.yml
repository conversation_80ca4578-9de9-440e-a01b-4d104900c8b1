# Go2 智能农业后端API - 开发环境配置
# 专为开发环境设计，支持热重载、调试和开发工具

services:
  # ================================
  # 主要API服务 (开发环境)
  # ================================
  go2-api:
    build:
      context: ..  # 使用项目根目录作为构建上下文
      dockerfile: backend/Dockerfile
      args:
        USER_ID: ${USER_ID:-1000}
        GROUP_ID: ${GROUP_ID:-1000}

    container_name: go2-api-dev
    restart: unless-stopped

    # 网络模式：host模式以访问局域网机器狗
    # 这是访问Go2机器狗的最佳方式
    network_mode: host

    # 代码挂载支持热重载
    volumes:
      - .:/app:cached                    # 代码目录 (热重载)
      - go2_dev_data:/app/data          # 数据持久化
      - go2_dev_logs:/app/logs          # 日志持久化
      - go2_dev_uploads:/app/uploads    # 上传文件持久化

    # 开发环境变量
    env_file:
      - .env

    environment:
      - PYTHONPATH=/app
      - ENVIRONMENT=development
      - API_RELOAD=true
      - LOG_LEVEL=DEBUG

    # 开发命令（启用热重载和调试）
    command: python start_server.py --host 0.0.0.0 --port 8000 --reload --log-level DEBUG

    # 健康检查
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # ================================
  # 开发数据库（可选PostgreSQL）
  # ================================
  postgres:
    image: postgres:15-alpine
    container_name: go2-postgres-dev
    restart: unless-stopped

    environment:
      POSTGRES_DB: go2_dev
      POSTGRES_USER: go2_user
      POSTGRES_PASSWORD: go2_dev_password
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"

    ports:
      - "5432:5432"

    volumes:
      - postgres_dev_data:/var/lib/postgresql/data

    networks:
      - go2_network

    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U go2_user -d go2_dev"]
      interval: 10s
      timeout: 5s
      retries: 5

    # 仅在需要时启用: docker-compose --profile postgres up
    profiles:
      - postgres

  # ================================
  # 开发工具：pgAdmin（PostgreSQL管理）
  # ================================
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: go2-pgadmin-dev
    restart: unless-stopped
    
    environment:
      - PGADMIN_DEFAULT_EMAIL=<EMAIL>
      - PGADMIN_DEFAULT_PASSWORD=admin123
    
    ports:
      - "8080:80"
    
    volumes:
      - pgadmin_dev_data:/var/lib/pgadmin
    
    networks:
      - go2_network
    
    depends_on:
      - postgres
    
    # 仅在需要时启用
    profiles:
      - postgres

  # ================================
  # 开发工具：Redis（可选缓存）
  # ================================
  redis:
    image: redis:7-alpine
    container_name: go2-redis-dev
    restart: unless-stopped
    
    ports:
      - "6379:6379"
    
    volumes:
      - redis_dev_data:/data
    
    networks:
      - go2_network
    
    # 仅在需要时启用
    profiles:
      - redis

# ================================
# 开发环境网络
# ================================
networks:
  go2_network:
    driver: bridge

# ================================
# 开发环境数据卷
# ================================
volumes:
  go2_dev_data:
    driver: local
  
  go2_dev_logs:
    driver: local
  
  go2_dev_uploads:
    driver: local
  
  postgres_dev_data:
    driver: local
  
  pgadmin_dev_data:
    driver: local
  
  redis_dev_data:
    driver: local
