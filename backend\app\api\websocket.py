"""
WebSocket实时通信端点
处理实时状态推送和命令传输
"""
import asyncio
import json
import logging
from typing import Dict, Set
from datetime import datetime

from fastapi import APIRouter, WebSocket, WebSocketDisconnect, Depends
from fastapi.websockets import WebSocketState

from app.models.schemas import WebSocketMessage, WebSocketCommand
from app.services.connection_manager import get_connection_manager, Go2ConnectionManager
from app.core.config import get_settings

logger = logging.getLogger(__name__)
router = APIRouter()

# 全局WebSocket连接管理
class WebSocketManager:
    """WebSocket连接管理器"""
    
    def __init__(self):
        self.active_connections: Set[WebSocket] = set()
        self.settings = get_settings()
    
    async def connect(self, websocket: WebSocket):
        """接受WebSocket连接"""
        await websocket.accept()
        self.active_connections.add(websocket)
        logger.info(f"WebSocket连接已建立，当前连接数: {len(self.active_connections)}")
    
    def disconnect(self, websocket: WebSocket):
        """断开WebSocket连接"""
        if websocket in self.active_connections:
            self.active_connections.remove(websocket)
        logger.info(f"WebSocket连接已断开，当前连接数: {len(self.active_connections)}")
    
    async def send_personal_message(self, message: dict, websocket: WebSocket):
        """发送个人消息"""
        if websocket.client_state == WebSocketState.CONNECTED:
            try:
                await websocket.send_text(json.dumps(message))
            except Exception as e:
                logger.error(f"发送个人消息失败: {e}")
                self.disconnect(websocket)
    
    async def broadcast(self, message: dict):
        """广播消息给所有连接的客户端"""
        if not self.active_connections:
            return
        
        disconnected = set()
        for connection in self.active_connections:
            try:
                if connection.client_state == WebSocketState.CONNECTED:
                    await connection.send_text(json.dumps(message))
                else:
                    disconnected.add(connection)
            except Exception as e:
                logger.error(f"广播消息失败: {e}")
                disconnected.add(connection)
        
        # 清理断开的连接
        for conn in disconnected:
            self.disconnect(conn)
    
    def get_connection_count(self) -> int:
        """获取当前连接数"""
        return len(self.active_connections)


# 全局WebSocket管理器实例
ws_manager = WebSocketManager()


def get_websocket_manager() -> WebSocketManager:
    """获取WebSocket管理器实例"""
    return ws_manager


@router.websocket("/robot")
async def websocket_endpoint(
    websocket: WebSocket,
    ws_mgr: WebSocketManager = Depends(get_websocket_manager),
    robot_mgr: Go2ConnectionManager = Depends(get_connection_manager)
):
    """
    机器狗WebSocket连接端点
    
    提供实时双向通信：
    - 接收客户端命令
    - 推送机器狗状态更新
    - 处理实时控制指令
    """
    await ws_mgr.connect(websocket)
    
    # 注册消息回调，将机器狗消息转发给WebSocket客户端
    def robot_message_callback(message, msgobj):
        """机器狗消息回调"""
        try:
            ws_message = {
                "type": "robot_message",
                "data": msgobj if msgobj else {"raw": message},
                "timestamp": datetime.now().isoformat()
            }
            # 异步广播消息
            asyncio.create_task(ws_mgr.broadcast(ws_message))
        except Exception as e:
            logger.error(f"处理机器狗消息回调时出错: {e}")
    
    # 添加回调
    robot_mgr.add_message_callback(robot_message_callback)
    
    try:
        # 发送连接成功消息
        await ws_mgr.send_personal_message({
            "type": "connection_established",
            "data": {
                "message": "WebSocket连接已建立",
                "robot_connected": robot_mgr.is_connected
            },
            "timestamp": datetime.now().isoformat()
        }, websocket)
        
        # 主消息循环
        while True:
            try:
                # 接收客户端消息
                data = await websocket.receive_text()
                message = json.loads(data)
                
                logger.debug(f"收到WebSocket消息: {message}")
                
                # 处理不同类型的消息
                await handle_websocket_message(message, websocket, robot_mgr, ws_mgr)
                
            except WebSocketDisconnect:
                logger.info("WebSocket客户端主动断开连接")
                break
            except json.JSONDecodeError:
                await ws_mgr.send_personal_message({
                    "type": "error",
                    "data": {"message": "无效的JSON格式"},
                    "timestamp": datetime.now().isoformat()
                }, websocket)
            except Exception as e:
                logger.error(f"处理WebSocket消息时出错: {e}")
                await ws_mgr.send_personal_message({
                    "type": "error", 
                    "data": {"message": f"处理消息时出错: {str(e)}"},
                    "timestamp": datetime.now().isoformat()
                }, websocket)
                
    except Exception as e:
        logger.error(f"WebSocket连接出错: {e}")
    finally:
        # 清理资源
        robot_mgr.remove_message_callback(robot_message_callback)
        ws_mgr.disconnect(websocket)


async def handle_websocket_message(
    message: dict, 
    websocket: WebSocket, 
    robot_mgr: Go2ConnectionManager,
    ws_mgr: WebSocketManager
):
    """处理WebSocket消息"""
    message_type = message.get("type", "")
    data = message.get("data", {})
    
    try:
        if message_type == "ping":
            # 心跳检测
            await ws_mgr.send_personal_message({
                "type": "pong",
                "data": {"timestamp": datetime.now().isoformat()},
                "timestamp": datetime.now().isoformat()
            }, websocket)
            
        elif message_type == "get_status":
            # 获取机器狗状态
            status = robot_mgr.get_status()
            await ws_mgr.send_personal_message({
                "type": "status_response",
                "data": status,
                "timestamp": datetime.now().isoformat()
            }, websocket)
            
        elif message_type == "movement":
            # 运动控制
            if not robot_mgr.is_connected:
                await ws_mgr.send_personal_message({
                    "type": "error",
                    "data": {"message": "机器狗未连接"},
                    "timestamp": datetime.now().isoformat()
                }, websocket)
                return
            
            x = data.get("x", 0.0)
            y = data.get("y", 0.0) 
            z = data.get("z", 0.0)
            
            success = await robot_mgr.send_command("Move", {"x": x, "y": y, "z": z})
            
            await ws_mgr.send_personal_message({
                "type": "command_response",
                "data": {
                    "success": success,
                    "command": "Move",
                    "parameters": {"x": x, "y": y, "z": z}
                },
                "timestamp": datetime.now().isoformat()
            }, websocket)
            
        elif message_type == "action":
            # 执行动作
            if not robot_mgr.is_connected:
                await ws_mgr.send_personal_message({
                    "type": "error",
                    "data": {"message": "机器狗未连接"},
                    "timestamp": datetime.now().isoformat()
                }, websocket)
                return
            
            action_name = data.get("action", "")
            parameters = data.get("parameters")
            
            success = await robot_mgr.send_command(action_name, parameters)
            
            await ws_mgr.send_personal_message({
                "type": "command_response",
                "data": {
                    "success": success,
                    "command": action_name,
                    "parameters": parameters
                },
                "timestamp": datetime.now().isoformat()
            }, websocket)
            
        elif message_type == "subscribe":
            # 订阅特定主题（暂时不实现，所有消息都会广播）
            await ws_mgr.send_personal_message({
                "type": "subscribe_response",
                "data": {"message": "订阅功能暂未实现，所有消息都会自动推送"},
                "timestamp": datetime.now().isoformat()
            }, websocket)
            
        else:
            # 未知消息类型
            await ws_mgr.send_personal_message({
                "type": "error",
                "data": {"message": f"未知消息类型: {message_type}"},
                "timestamp": datetime.now().isoformat()
            }, websocket)
            
    except Exception as e:
        logger.error(f"处理WebSocket消息时出错: {e}")
        await ws_mgr.send_personal_message({
            "type": "error",
            "data": {"message": f"处理消息失败: {str(e)}"},
            "timestamp": datetime.now().isoformat()
        }, websocket)


@router.get("/connections")
async def get_websocket_connections(
    ws_mgr: WebSocketManager = Depends(get_websocket_manager)
):
    """获取当前WebSocket连接数"""
    return {
        "active_connections": ws_mgr.get_connection_count(),
        "timestamp": datetime.now().isoformat()
    }
