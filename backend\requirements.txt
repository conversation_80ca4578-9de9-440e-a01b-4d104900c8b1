# FastAPI and related dependencies
fastapi==0.104.1
uvicorn[standard]==0.24.0
websockets==12.0
pydantic==2.5.0
pydantic-settings==2.1.0

# Existing go2_webrtc dependencies
aiortc==1.6.0
aiohttp==3.9.1
paho-mqtt==1.6.1
python-dotenv==1.0.0
pygame==2.5.2
wasmtime==14.0.0
pycryptodome==3.19.0
requests==2.31.0

# Additional go2_webrtc dependencies (missing from setup.py)
numpy==1.26.2
opencv-python==********

# Additional backend dependencies
python-multipart==0.0.6
jinja2==3.1.2
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
sqlalchemy==2.0.23
alembic==1.13.1

# Development and testing dependencies
pytest==7.4.3
pytest-asyncio==0.21.1
httpx==0.25.2
black==23.11.0
isort==5.12.0
flake8==6.1.0
