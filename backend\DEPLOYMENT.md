# Go2 WebRTC API 部署指南

本文档详细说明如何在不同环境中部署Go2 WebRTC API后端服务器。

## 系统要求

### 最低要求
- Python 3.8+
- 内存: 512MB
- 存储: 1GB
- 网络: 与机器狗在同一局域网

### 推荐配置
- Python 3.9+
- 内存: 2GB+
- 存储: 5GB+
- CPU: 2核心+

## 部署方式

### 1. 开发环境部署

适用于开发和测试环境。

```bash
# 1. 克隆项目
git clone <repository-url>
cd go2-webrtc-master/backend

# 2. 创建虚拟环境
python -m venv venv
source venv/bin/activate  # Linux/Mac
# 或
venv\Scripts\activate     # Windows

# 3. 安装依赖
pip install -r requirements.txt

# 4. 配置环境变量
cp .env.example .env
# 编辑 .env 文件，设置机器狗IP等参数

# 5. 启动开发服务器
python start_server.py --reload --log-level DEBUG
```

### 2. 生产环境部署

#### 方式一：直接部署

```bash
# 1. 安装依赖
pip install -r requirements.txt

# 2. 配置环境变量
export GO2_IP="*************"
export GO2_TOKEN="your_token_here"
export API_HOST="0.0.0.0"
export API_PORT="8000"
export LOG_LEVEL="INFO"

# 3. 启动生产服务器
python start_server.py --workers 4 --host 0.0.0.0 --port 8000
```

#### 方式二：使用Gunicorn

```bash
# 1. 安装Gunicorn
pip install gunicorn

# 2. 启动服务器
gunicorn app.main:app \
  -w 4 \
  -k uvicorn.workers.UvicornWorker \
  --bind 0.0.0.0:8000 \
  --access-logfile - \
  --error-logfile -
```

#### 方式三：使用Supervisor

创建Supervisor配置文件 `/etc/supervisor/conf.d/go2-api.conf`：

```ini
[program:go2-api]
command=/path/to/venv/bin/python start_server.py --host 0.0.0.0 --port 8000
directory=/path/to/go2-webrtc-master/backend
user=www-data
autostart=true
autorestart=true
redirect_stderr=true
stdout_logfile=/var/log/go2-api.log
environment=GO2_IP="*************",GO2_TOKEN="your_token"
```

启动服务：
```bash
sudo supervisorctl reread
sudo supervisorctl update
sudo supervisorctl start go2-api
```

### 3. Docker部署

#### 创建Dockerfile

```dockerfile
FROM python:3.9-slim

# 设置工作目录
WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    gcc \
    && rm -rf /var/lib/apt/lists/*

# 复制依赖文件
COPY requirements.txt .

# 安装Python依赖
RUN pip install --no-cache-dir -r requirements.txt

# 复制应用代码
COPY . .

# 创建非root用户
RUN useradd -m -u 1000 appuser && chown -R appuser:appuser /app
USER appuser

# 暴露端口
EXPOSE 8000

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:8000/health || exit 1

# 启动命令
CMD ["python", "start_server.py", "--host", "0.0.0.0", "--port", "8000"]
```

#### 构建和运行

```bash
# 构建镜像
docker build -t go2-webrtc-api .

# 运行容器
docker run -d \
  --name go2-api \
  -p 8000:8000 \
  -e GO2_IP="*************" \
  -e GO2_TOKEN="your_token" \
  --restart unless-stopped \
  go2-webrtc-api
```

#### Docker Compose

创建 `docker-compose.yml`：

```yaml
version: '3.8'

services:
  go2-api:
    build: .
    ports:
      - "8000:8000"
    environment:
      - GO2_IP=*************
      - GO2_TOKEN=your_token_here
      - LOG_LEVEL=INFO
    volumes:
      - ./logs:/app/logs
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - go2-api
    restart: unless-stopped
```

启动服务：
```bash
docker-compose up -d
```

### 4. 云平台部署

#### AWS EC2

1. 启动EC2实例（推荐t3.small或更高配置）
2. 安装Docker或直接部署
3. 配置安全组，开放8000端口
4. 使用Elastic IP确保固定IP地址

#### 阿里云ECS

1. 创建ECS实例
2. 配置安全组规则
3. 使用云监控设置告警
4. 配置负载均衡（如需要）

## 网络配置

### 防火墙设置

```bash
# Ubuntu/Debian
sudo ufw allow 8000/tcp

# CentOS/RHEL
sudo firewall-cmd --permanent --add-port=8000/tcp
sudo firewall-cmd --reload
```

### Nginx反向代理

创建 `/etc/nginx/sites-available/go2-api`：

```nginx
server {
    listen 80;
    server_name your-domain.com;

    location / {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    location /ws/ {
        proxy_pass http://127.0.0.1:8000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

启用站点：
```bash
sudo ln -s /etc/nginx/sites-available/go2-api /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

## 监控和日志

### 日志配置

```bash
# 设置日志目录
export LOG_DIR="/var/log/go2-api"
mkdir -p $LOG_DIR

# 启动时指定日志配置
python start_server.py --log-level INFO
```

### 系统监控

使用Prometheus + Grafana监控：

```yaml
# prometheus.yml
scrape_configs:
  - job_name: 'go2-api'
    static_configs:
      - targets: ['localhost:8000']
    metrics_path: '/metrics'
```

### 健康检查

```bash
# 简单健康检查
curl -f http://localhost:8000/health

# 详细状态检查
curl http://localhost:8000/api/status
```

## 安全配置

### HTTPS配置

使用Let's Encrypt获取SSL证书：

```bash
sudo apt install certbot python3-certbot-nginx
sudo certbot --nginx -d your-domain.com
```

### 环境变量安全

```bash
# 使用专用的环境文件
sudo chmod 600 .env

# 或使用系统环境变量
export GO2_TOKEN="$(cat /etc/go2-api/token)"
```

### 访问控制

在Nginx中添加IP白名单：

```nginx
location / {
    allow ***********/24;
    deny all;
    proxy_pass http://127.0.0.1:8000;
}
```

## 故障排除

### 常见问题

1. **端口占用**
   ```bash
   sudo netstat -tlnp | grep :8000
   sudo kill -9 <PID>
   ```

2. **权限问题**
   ```bash
   sudo chown -R $USER:$USER /path/to/project
   chmod +x start_server.py
   ```

3. **依赖问题**
   ```bash
   pip install --upgrade pip
   pip install -r requirements.txt --force-reinstall
   ```

4. **网络连接问题**
   ```bash
   ping *************  # 测试机器狗连接
   telnet ************* 8081  # 测试端口连通性
   ```

### 日志分析

```bash
# 查看应用日志
tail -f /var/log/go2-api.log

# 查看错误日志
grep ERROR /var/log/go2-api.log

# 查看连接日志
grep "connection" /var/log/go2-api.log
```

## 性能优化

### 系统优化

```bash
# 增加文件描述符限制
echo "* soft nofile 65536" >> /etc/security/limits.conf
echo "* hard nofile 65536" >> /etc/security/limits.conf

# 优化网络参数
echo "net.core.somaxconn = 65535" >> /etc/sysctl.conf
sysctl -p
```

### 应用优化

```python
# 在配置中调整参数
WS_MAX_CONNECTIONS = 1000
ROBOT_COMMAND_TIMEOUT = 3.0
```

## 备份和恢复

### 配置备份

```bash
# 备份配置文件
tar -czf go2-api-config-$(date +%Y%m%d).tar.gz .env config.yaml

# 备份数据库（如果使用）
sqlite3 go2_api.db .dump > backup.sql
```

### 自动备份脚本

```bash
#!/bin/bash
# backup.sh
BACKUP_DIR="/backup/go2-api"
DATE=$(date +%Y%m%d_%H%M%S)

mkdir -p $BACKUP_DIR
tar -czf $BACKUP_DIR/go2-api-$DATE.tar.gz \
  .env config.yaml logs/ go2_api.db

# 保留最近7天的备份
find $BACKUP_DIR -name "*.tar.gz" -mtime +7 -delete
```

## 维护建议

1. **定期更新依赖**：每月检查并更新Python包
2. **监控日志**：设置日志轮转和监控告警
3. **性能测试**：定期进行负载测试
4. **安全审计**：定期检查安全配置
5. **备份验证**：定期验证备份文件的完整性

通过以上部署指南，您可以在各种环境中成功部署Go2 WebRTC API服务器。如有问题，请参考故障排除部分或查看项目文档。
