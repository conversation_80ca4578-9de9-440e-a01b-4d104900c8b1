"""
IMU数据API
提供机器狗IMU和低级状态数据的获取接口
"""
import logging
from typing import Optional
from fastapi import APIRouter, HTTPException, Depends
from datetime import datetime

from app.models.schemas import (
    BaseResponse, 
    ErrorResponse, 
    IMUDataResponse,
    LowStateData,
    IMUState,
    MotorState,
    BMSState
)
from app.services.connection_manager import get_connection_manager, Go2ConnectionManager

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/imu", tags=["IMU"])


@router.get("/status", response_model=IMUDataResponse, summary="获取IMU状态数据")
async def get_imu_status(
    connection_manager: Go2ConnectionManager = Depends(get_connection_manager)
):
    """
    获取机器狗的IMU状态数据，包括：
    - IMU姿态数据 (Roll, Pitch, Yaw)
    - 电机状态
    - 电池管理系统状态
    - 足部力传感器数据
    - 温度和电压信息
    """
    try:
        if not connection_manager.is_connected:
            raise HTTPException(
                status_code=503, 
                detail="机器狗未连接，请先建立连接"
            )
        
        # 获取缓存的状态数据
        status = connection_manager.get_status()
        cached_data = status.get("cached_data", {})
        
        # 查找低级状态数据
        lowstate_topic = "rt/lf/lowstate"
        lowstate_data = cached_data.get(lowstate_topic)
        
        if not lowstate_data:
            return IMUDataResponse(
                success=True,
                message="暂无IMU数据",
                data=None,
                topic=lowstate_topic,
                last_updated=None
            )
        
        # 解析数据
        raw_data = lowstate_data.get("data", {})
        timestamp = lowstate_data.get("timestamp")
        
        # 验证数据结构
        if not raw_data or "imu_state" not in raw_data:
            return IMUDataResponse(
                success=True,
                message="IMU数据格式不完整",
                data=None,
                topic=lowstate_topic,
                last_updated=timestamp
            )
        
        # 构建响应数据
        try:
            # 解析IMU状态
            imu_state = IMUState(**raw_data["imu_state"])
            
            # 解析电机状态
            motor_states = []
            for motor_data in raw_data.get("motor_state", []):
                motor_states.append(MotorState(**motor_data))
            
            # 解析BMS状态
            bms_state = BMSState(**raw_data["bms_state"])
            
            # 构建完整的低级状态数据
            lowstate = LowStateData(
                imu_state=imu_state,
                motor_state=motor_states,
                bms_state=bms_state,
                foot_force=raw_data.get("foot_force", []),
                temperature_ntc1=raw_data.get("temperature_ntc1", 0),
                power_v=raw_data.get("power_v", 0.0)
            )
            
            return IMUDataResponse(
                success=True,
                message="IMU数据获取成功",
                data=lowstate,
                topic=lowstate_topic,
                last_updated=timestamp
            )
            
        except Exception as parse_error:
            logger.error(f"解析IMU数据失败: {parse_error}")
            return IMUDataResponse(
                success=False,
                message=f"IMU数据解析失败: {str(parse_error)}",
                data=None,
                topic=lowstate_topic,
                last_updated=timestamp
            )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取IMU状态失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"获取IMU状态失败: {str(e)}"
        )


@router.get("/rpy", summary="获取IMU姿态角")
async def get_imu_rpy(
    connection_manager: Go2ConnectionManager = Depends(get_connection_manager)
):
    """
    获取机器狗的IMU姿态角 (Roll, Pitch, Yaw)
    
    Returns:
        dict: 包含roll, pitch, yaw的字典
    """
    try:
        if not connection_manager.is_connected:
            raise HTTPException(
                status_code=503, 
                detail="机器狗未连接，请先建立连接"
            )
        
        # 获取缓存的状态数据
        status = connection_manager.get_status()
        cached_data = status.get("cached_data", {})
        
        # 查找低级状态数据
        lowstate_data = cached_data.get("rt/lf/lowstate")
        
        if not lowstate_data:
            return {
                "success": False,
                "message": "暂无IMU数据",
                "data": None,
                "timestamp": datetime.now().isoformat()
            }
        
        raw_data = lowstate_data.get("data", {})
        imu_data = raw_data.get("imu_state", {})
        rpy = imu_data.get("rpy", [0.0, 0.0, 0.0])
        
        return {
            "success": True,
            "message": "IMU姿态角获取成功",
            "data": {
                "roll": rpy[0] if len(rpy) > 0 else 0.0,
                "pitch": rpy[1] if len(rpy) > 1 else 0.0,
                "yaw": rpy[2] if len(rpy) > 2 else 0.0,
                "rpy_array": rpy
            },
            "timestamp": lowstate_data.get("timestamp"),
            "last_updated": datetime.now().isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取IMU姿态角失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"获取IMU姿态角失败: {str(e)}"
        )


@router.get("/motors", summary="获取电机状态")
async def get_motor_status(
    connection_manager: Go2ConnectionManager = Depends(get_connection_manager)
):
    """
    获取机器狗的电机状态信息
    
    Returns:
        dict: 包含所有电机状态的字典
    """
    try:
        if not connection_manager.is_connected:
            raise HTTPException(
                status_code=503, 
                detail="机器狗未连接，请先建立连接"
            )
        
        # 获取缓存的状态数据
        status = connection_manager.get_status()
        cached_data = status.get("cached_data", {})
        
        # 查找低级状态数据
        lowstate_data = cached_data.get("rt/lf/lowstate")
        
        if not lowstate_data:
            return {
                "success": False,
                "message": "暂无电机数据",
                "data": None,
                "timestamp": datetime.now().isoformat()
            }
        
        raw_data = lowstate_data.get("data", {})
        motor_data = raw_data.get("motor_state", [])
        
        return {
            "success": True,
            "message": "电机状态获取成功",
            "data": {
                "motor_count": len(motor_data),
                "motors": motor_data,
                "active_motors": [i for i, motor in enumerate(motor_data) if motor.get("temperature", 0) > 0]
            },
            "timestamp": lowstate_data.get("timestamp"),
            "last_updated": datetime.now().isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取电机状态失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"获取电机状态失败: {str(e)}"
        )


@router.get("/battery", summary="获取电池状态")
async def get_battery_status(
    connection_manager: Go2ConnectionManager = Depends(get_connection_manager)
):
    """
    获取机器狗的电池状态信息
    
    Returns:
        dict: 包含电池状态的字典
    """
    try:
        if not connection_manager.is_connected:
            raise HTTPException(
                status_code=503, 
                detail="机器狗未连接，请先建立连接"
            )
        
        # 获取缓存的状态数据
        status = connection_manager.get_status()
        cached_data = status.get("cached_data", {})
        
        # 查找低级状态数据
        lowstate_data = cached_data.get("rt/lf/lowstate")
        
        if not lowstate_data:
            return {
                "success": False,
                "message": "暂无电池数据",
                "data": None,
                "timestamp": datetime.now().isoformat()
            }
        
        raw_data = lowstate_data.get("data", {})
        bms_data = raw_data.get("bms_state", {})
        power_v = raw_data.get("power_v", 0.0)
        
        return {
            "success": True,
            "message": "电池状态获取成功",
            "data": {
                "soc": bms_data.get("soc", 0),  # 电量百分比
                "voltage": power_v,  # 电压
                "current": bms_data.get("current", 0),  # 电流
                "cycle": bms_data.get("cycle", 0),  # 充电循环次数
                "temperature": {
                    "bq_ntc": bms_data.get("bq_ntc", []),
                    "mcu_ntc": bms_data.get("mcu_ntc", [])
                },
                "version": {
                    "high": bms_data.get("version_high", 0),
                    "low": bms_data.get("version_low", 0)
                }
            },
            "timestamp": lowstate_data.get("timestamp"),
            "last_updated": datetime.now().isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取电池状态失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"获取电池状态失败: {str(e)}"
        )
