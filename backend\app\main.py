"""
Go2 WebRTC API 主应用
FastAPI应用入口点
"""
import logging
import sys
import os
from contextlib import asynccontextmanager

from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse

# 添加python目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "../../python"))

from app.core.config import get_settings
from app.services.connection_manager import get_connection_manager
from app.api import connection, movement, websocket, imu

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

settings = get_settings()


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    logger.info("启动Go2 WebRTC API服务器...")
    
    # 启动时的初始化
    connection_manager = get_connection_manager()
    logger.info(f"连接管理器已初始化")
    
    yield
    
    # 关闭时的清理
    logger.info("正在关闭Go2 WebRTC API服务器...")
    if connection_manager.is_connected:
        await connection_manager.disconnect()
    logger.info("服务器已关闭")


# 创建FastAPI应用
app = FastAPI(
    title="Go2 WebRTC API",
    description="Unitree Go2机器狗WebRTC控制API - 前后端分离架构",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc",
    lifespan=lifespan
)

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 在生产环境中应该限制具体域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


# 全局异常处理
@app.exception_handler(Exception)
async def global_exception_handler(request, exc):
    logger.error(f"全局异常: {exc}")
    return JSONResponse(
        status_code=500,
        content={"detail": "内部服务器错误", "error": str(exc)}
    )


# 健康检查端点
@app.get("/health")
async def health_check():
    """健康检查"""
    return {
        "status": "healthy",
        "service": "Go2 WebRTC API",
        "version": "1.0.0"
    }


# 根路径
@app.get("/")
async def root():
    """API根路径"""
    return {
        "message": "Go2 WebRTC API - 前后端分离架构",
        "docs": "/docs",
        "health": "/health",
        "version": "1.0.0"
    }


# 包含API路由
app.include_router(connection.router, prefix="/api", tags=["连接管理"])
app.include_router(movement.router, prefix="/api", tags=["运动控制"])
app.include_router(imu.router, tags=["IMU数据"])
app.include_router(websocket.router, prefix="/ws", tags=["WebSocket"])


if __name__ == "__main__":
    import uvicorn
    
    logger.info(f"启动服务器 - 地址: {settings.api_host}:{settings.api_port}")
    uvicorn.run(
        "main:app",
        host=settings.api_host,
        port=settings.api_port,
        reload=settings.api_reload,
        log_level=settings.log_level.lower()
    )
