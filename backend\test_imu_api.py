"""
IMU API测试脚本
用于测试IMU数据获取API的功能
"""
import asyncio
import aiohttp
import json
from datetime import datetime

BASE_URL = "http://localhost:8000"

async def test_imu_api():
    """测试IMU API功能"""
    async with aiohttp.ClientSession() as session:
        
        print("=== Go2 IMU API 测试 ===")
        print(f"测试时间: {datetime.now()}")
        print(f"API地址: {BASE_URL}")
        print()
        
        # 1. 测试健康检查
        print("1. 测试健康检查...")
        try:
            async with session.get(f"{BASE_URL}/health") as resp:
                if resp.status == 200:
                    data = await resp.json()
                    print(f"✓ 健康检查通过: {data}")
                else:
                    print(f"✗ 健康检查失败: {resp.status}")
        except Exception as e:
            print(f"✗ 健康检查异常: {e}")
        print()
        
        # 2. 测试连接状态
        print("2. 测试连接状态...")
        try:
            async with session.get(f"{BASE_URL}/api/connection/status") as resp:
                if resp.status == 200:
                    data = await resp.json()
                    print(f"✓ 连接状态: {data.get('connected', False)}")
                    if not data.get('connected', False):
                        print("⚠ 机器狗未连接，IMU数据可能为空")
                else:
                    print(f"✗ 获取连接状态失败: {resp.status}")
        except Exception as e:
            print(f"✗ 连接状态检查异常: {e}")
        print()
        
        # 3. 测试IMU完整状态
        print("3. 测试IMU完整状态...")
        try:
            async with session.get(f"{BASE_URL}/api/imu/status") as resp:
                if resp.status == 200:
                    data = await resp.json()
                    print(f"✓ IMU状态获取成功")
                    print(f"  - 成功: {data.get('success', False)}")
                    print(f"  - 消息: {data.get('message', 'N/A')}")
                    print(f"  - 主题: {data.get('topic', 'N/A')}")
                    print(f"  - 最后更新: {data.get('last_updated', 'N/A')}")
                    
                    if data.get('data'):
                        imu_data = data['data']
                        print(f"  - IMU数据:")
                        if 'imu_state' in imu_data:
                            rpy = imu_data['imu_state'].get('rpy', [])
                            print(f"    Roll: {rpy[0] if len(rpy) > 0 else 'N/A'}")
                            print(f"    Pitch: {rpy[1] if len(rpy) > 1 else 'N/A'}")
                            print(f"    Yaw: {rpy[2] if len(rpy) > 2 else 'N/A'}")
                        
                        if 'bms_state' in imu_data:
                            bms = imu_data['bms_state']
                            print(f"  - 电池电量: {bms.get('soc', 'N/A')}%")
                            print(f"  - 电池电流: {bms.get('current', 'N/A')}")
                        
                        if 'motor_state' in imu_data:
                            motors = imu_data['motor_state']
                            active_motors = [i for i, m in enumerate(motors) if m.get('temperature', 0) > 0]
                            print(f"  - 活跃电机数: {len(active_motors)}/{len(motors)}")
                    else:
                        print("  - 暂无IMU数据")
                else:
                    print(f"✗ IMU状态获取失败: {resp.status}")
                    error_text = await resp.text()
                    print(f"  错误信息: {error_text}")
        except Exception as e:
            print(f"✗ IMU状态获取异常: {e}")
        print()
        
        # 4. 测试IMU姿态角
        print("4. 测试IMU姿态角...")
        try:
            async with session.get(f"{BASE_URL}/api/imu/rpy") as resp:
                if resp.status == 200:
                    data = await resp.json()
                    print(f"✓ IMU姿态角获取成功")
                    if data.get('data'):
                        rpy_data = data['data']
                        print(f"  - Roll: {rpy_data.get('roll', 'N/A')}")
                        print(f"  - Pitch: {rpy_data.get('pitch', 'N/A')}")
                        print(f"  - Yaw: {rpy_data.get('yaw', 'N/A')}")
                        print(f"  - 数组: {rpy_data.get('rpy_array', 'N/A')}")
                    else:
                        print("  - 暂无姿态角数据")
                else:
                    print(f"✗ IMU姿态角获取失败: {resp.status}")
        except Exception as e:
            print(f"✗ IMU姿态角获取异常: {e}")
        print()
        
        # 5. 测试电机状态
        print("5. 测试电机状态...")
        try:
            async with session.get(f"{BASE_URL}/api/imu/motors") as resp:
                if resp.status == 200:
                    data = await resp.json()
                    print(f"✓ 电机状态获取成功")
                    if data.get('data'):
                        motor_data = data['data']
                        print(f"  - 电机总数: {motor_data.get('motor_count', 'N/A')}")
                        print(f"  - 活跃电机: {motor_data.get('active_motors', 'N/A')}")
                    else:
                        print("  - 暂无电机数据")
                else:
                    print(f"✗ 电机状态获取失败: {resp.status}")
        except Exception as e:
            print(f"✗ 电机状态获取异常: {e}")
        print()
        
        # 6. 测试电池状态
        print("6. 测试电池状态...")
        try:
            async with session.get(f"{BASE_URL}/api/imu/battery") as resp:
                if resp.status == 200:
                    data = await resp.json()
                    print(f"✓ 电池状态获取成功")
                    if data.get('data'):
                        battery_data = data['data']
                        print(f"  - 电量: {battery_data.get('soc', 'N/A')}%")
                        print(f"  - 电压: {battery_data.get('voltage', 'N/A')}V")
                        print(f"  - 电流: {battery_data.get('current', 'N/A')}")
                        print(f"  - 充电循环: {battery_data.get('cycle', 'N/A')}")
                    else:
                        print("  - 暂无电池数据")
                else:
                    print(f"✗ 电池状态获取失败: {resp.status}")
        except Exception as e:
            print(f"✗ 电池状态获取异常: {e}")
        print()
        
        print("=== 测试完成 ===")


if __name__ == "__main__":
    asyncio.run(test_imu_api())
