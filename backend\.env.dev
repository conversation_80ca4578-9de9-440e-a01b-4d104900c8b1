# Go2 智能农业后端API - 开发环境配置
# 专为Docker开发环境设计的环境变量配置 (Python 3.12)

# ================================
# 环境标识
# ================================
ENVIRONMENT=development

# ================================
# Go2机器狗连接配置
# ================================
GO2_IP=*************
GO2_TOKEN=

# ================================
# API服务器配置 (开发环境)
# ================================
API_HOST=0.0.0.0
API_PORT=8787
API_RELOAD=true

# ================================
# 日志配置 (开发环境)
# ================================
LOG_LEVEL=DEBUG
LOG_FORMAT=json

# ================================
# 数据库配置
# ================================
# SQLite (默认开发数据库)
DATABASE_URL=sqlite:///./data/go2_api.db

# PostgreSQL (可选，需要启用postgres profile)
# DATABASE_URL=****************************************************/go2_dev

# ================================
# 安全配置 (开发环境)
# ================================
SECRET_KEY=dev-secret-key-change-in-production
ACCESS_TOKEN_EXPIRE_MINUTES=30

# ================================
# WebSocket配置
# ================================
WS_HEARTBEAT_INTERVAL=30
WS_MAX_CONNECTIONS=100

# ================================
# 机器狗控制配置
# ================================
ROBOT_COMMAND_TIMEOUT=5.0
ROBOT_CONNECTION_RETRY=3
ROBOT_HEARTBEAT_INTERVAL=10.0

# ================================
# 开发工具配置
# ================================
# 启用开发模式特性
ENABLE_DEBUG_TOOLBAR=true
ENABLE_CORS_ALL=true

# 调试端口 (可选)
DEBUG_PORT=5678

# ================================
# Docker特定配置
# ================================
# 用户ID映射 (Linux/Mac)
USER_ID=1000
GROUP_ID=1000

# 数据目录
DATA_DIR=/app/data
LOGS_DIR=/app/logs
UPLOADS_DIR=/app/uploads

# ================================
# 可选服务配置
# ================================
# Redis配置 (如果启用redis profile)
REDIS_URL=redis://redis:6379/0

# PostgreSQL配置 (如果启用postgres profile)
POSTGRES_HOST=postgres
POSTGRES_PORT=5432
POSTGRES_DB=go2_dev
POSTGRES_USER=go2_user
POSTGRES_PASSWORD=go2_dev_password
