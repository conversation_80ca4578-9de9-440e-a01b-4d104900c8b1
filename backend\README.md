# Go2 WebRTC API 后端服务器

基于FastAPI的独立后端API服务器，用于控制Unitree Go2机器狗。实现了前后端完全分离的架构，提供RESTful API和WebSocket实时通信。

## 功能特性

- 🚀 **独立运行**: 无需前端即可启动和运行
- 🔌 **RESTful API**: 完整的HTTP API接口
- ⚡ **WebSocket**: 实时双向通信
- 📚 **自动文档**: OpenAPI/Swagger文档
- 🛡️ **错误处理**: 完善的错误处理和日志记录
- 🔄 **状态管理**: 智能状态缓存和广播
- 🎛️ **配置管理**: 灵活的配置选项

## 快速开始

### 1. 安装依赖

```bash
cd backend
pip install -r requirements.txt
```

### 2. 配置环境

复制环境变量模板：
```bash
cp .env.example .env
```

编辑 `.env` 文件，设置机器狗IP地址：
```env
GO2_IP=*************
GO2_TOKEN=your_token_here
```

### 3. 启动服务器

```bash
python start_server.py
```

或者使用uvicorn直接启动：
```bash
uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
```

### 4. 访问API文档

- Swagger UI: http://localhost:8000/docs
- ReDoc: http://localhost:8000/redoc
- 健康检查: http://localhost:8000/health

## API 使用示例

### 连接管理

#### 连接到机器狗
```bash
curl -X POST "http://localhost:8000/api/connect" \
  -H "Content-Type: application/json" \
  -d '{
    "robot_ip": "*************",
    "token": "your_token_here"
  }'
```

#### 查看连接状态
```bash
curl -X GET "http://localhost:8000/api/status"
```

#### 断开连接
```bash
curl -X DELETE "http://localhost:8000/api/disconnect"
```

### 运动控制

#### 基本运动控制
```bash
# 前进
curl -X POST "http://localhost:8000/api/movement" \
  -H "Content-Type: application/json" \
  -d '{
    "x": 0.5,
    "y": 0.0,
    "z": 0.0
  }'

# 停止运动
curl -X POST "http://localhost:8000/api/stop"
```

#### 执行预定义动作
```bash
# 站起
curl -X POST "http://localhost:8000/api/action" \
  -H "Content-Type: application/json" \
  -d '{
    "action": "StandUp"
  }'

# 打招呼
curl -X POST "http://localhost:8000/api/action" \
  -H "Content-Type: application/json" \
  -d '{
    "action": "Hello"
  }'
```

#### 获取可用命令
```bash
curl -X GET "http://localhost:8000/api/commands"
```

### WebSocket 实时通信

#### JavaScript客户端示例
```javascript
const ws = new WebSocket('ws://localhost:8000/ws/robot');

ws.onopen = function(event) {
    console.log('WebSocket连接已建立');
    
    // 发送心跳
    ws.send(JSON.stringify({
        type: 'ping'
    }));
};

ws.onmessage = function(event) {
    const message = JSON.parse(event.data);
    console.log('收到消息:', message);
};

// 发送运动命令
function sendMovement(x, y, z) {
    ws.send(JSON.stringify({
        type: 'movement',
        data: { x, y, z }
    }));
}

// 执行动作
function executeAction(action) {
    ws.send(JSON.stringify({
        type: 'action',
        data: { action }
    }));
}
```

#### Python客户端示例
```python
import asyncio
import websockets
import json

async def websocket_client():
    uri = "ws://localhost:8000/ws/robot"
    
    async with websockets.connect(uri) as websocket:
        # 发送心跳
        await websocket.send(json.dumps({
            "type": "ping"
        }))
        
        # 监听消息
        async for message in websocket:
            data = json.loads(message)
            print(f"收到消息: {data}")

# 运行客户端
asyncio.run(websocket_client())
```

## 配置选项

### 环境变量

| 变量名 | 描述 | 默认值 |
|--------|------|--------|
| `GO2_IP` | 机器狗IP地址 | `*************` |
| `GO2_TOKEN` | 认证令牌 | `""` |
| `API_HOST` | 服务器监听地址 | `0.0.0.0` |
| `API_PORT` | 服务器端口 | `8000` |
| `LOG_LEVEL` | 日志级别 | `INFO` |

### 命令行参数

```bash
python start_server.py --help
```

常用参数：
- `--host`: 指定监听地址
- `--port`: 指定端口
- `--reload`: 开发模式（自动重载）
- `--log-level`: 设置日志级别
- `--robot-ip`: 机器狗IP地址
- `--robot-token`: 机器狗认证令牌

## 项目结构

```
backend/
├── app/
│   ├── api/                 # API路由
│   │   ├── connection.py    # 连接管理API
│   │   ├── movement.py      # 运动控制API
│   │   └── websocket.py     # WebSocket端点
│   ├── core/                # 核心模块
│   │   ├── config.py        # 配置管理
│   │   └── logging.py       # 日志配置
│   ├── models/              # 数据模型
│   │   └── schemas.py       # Pydantic模型
│   ├── services/            # 业务服务
│   │   ├── connection_manager.py  # 连接管理器
│   │   └── state_manager.py       # 状态管理器
│   └── main.py              # FastAPI应用
├── tests/                   # 测试文件
├── requirements.txt         # 依赖列表
├── .env.example            # 环境变量模板
├── config.yaml             # 配置文件
├── start_server.py         # 启动脚本
└── README.md               # 说明文档
```

## 开发指南

### 添加新的API端点

1. 在 `app/api/` 目录下创建新的路由文件
2. 在 `app/models/schemas.py` 中定义数据模型
3. 在 `app/main.py` 中包含新的路由

### 扩展WebSocket功能

1. 在 `app/api/websocket.py` 中添加新的消息处理器
2. 更新 `handle_websocket_message` 函数

### 自定义配置

1. 修改 `app/core/config.py` 中的 `Settings` 类
2. 更新 `.env.example` 文件
3. 在 `config.yaml` 中添加新的配置项

## 故障排除

### 常见问题

1. **连接失败**: 检查机器狗IP地址和网络连接
2. **模块导入错误**: 确保已安装所有依赖
3. **端口占用**: 使用 `--port` 参数指定其他端口
4. **权限错误**: 确保有足够的权限访问网络和文件

### 日志查看

```bash
# 查看详细日志
python start_server.py --log-level DEBUG

# 查看特定模块日志
export LOG_LEVEL=DEBUG
python start_server.py
```

## 部署建议

### 生产环境

```bash
# 使用多个工作进程
python start_server.py --workers 4 --host 0.0.0.0 --port 8000

# 或使用gunicorn
pip install gunicorn
gunicorn app.main:app -w 4 -k uvicorn.workers.UvicornWorker --bind 0.0.0.0:8000
```

### Docker部署

```dockerfile
FROM python:3.9-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .
EXPOSE 8000

CMD ["python", "start_server.py", "--host", "0.0.0.0", "--port", "8000"]
```

## 许可证

本项目基于BSD 2-Clause License开源。详见 [LICENSE](../LICENSE) 文件。
