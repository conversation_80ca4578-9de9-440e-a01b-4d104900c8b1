"""
配置管理模块
处理环境变量和应用配置
"""
import os
from typing import Optional
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """应用配置类"""
    
    # Go2机器狗连接配置
    go2_ip: str = "************"
    go2_token: str = "eyJkYXRhMSI6Ik9rOENIbDdrOGZNSUlCSURBTkJna3Foa2lHOXcwQkFRRUZBQU9DQVEwQU1JSUJDQUtDQVFFQXM1Q1ptZkdGVmhKYVlWMWZXS0V6dmFpYlpzUndKVi9QSkhteHd1QVZhNE9uaTZWbCt4bmc0L1dVQk5CVGhiMUNjZUtMMHEzN0NkZ2doOUNtYS9YNjYwNldJTWdOUmlpVmNrZFMzZUNQTzBUS2NuQUE0eE1reWtzeVBBaGlrejhoaWEvQ1BvLy9vcVdPVmlmZnM4K09uWjlHY1VLZVA3S2hvVU0yVENtRy9pTjB5Nm9RNGxIRURnc3dlMUJ5cVRFbEtHQldqcFdyeFRHaXhzYXZMQVZuZmhTSldoMmlFU0k0S0tHVTN5bm1qK3Q3MUt1NFFaQlA0bmNmWGdNTXZ5bWRxUHhQc2h6REZIbWhJUXNHVnllOXZYSmxOMzNVTVpWcTB2MEU2WGFaSlZvVXRHK3hJUjRROXhGelVyWVpUMkswSmh2cGlHSHFQcm90NFJmSGhTVkprUUlCRVE9PUNHQkdFSlFDN0YifQ=="
    
    # API服务器配置
    api_host: str = "0.0.0.0"
    api_port: int = 8787
    api_reload: bool = False
    
    # 日志配置
    log_level: str = "INFO"
    log_format: str = "json"
    
    # 数据库配置
    database_url: str = "sqlite:///./go2_api.db"
    
    # 安全配置
    secret_key: str = "your-secret-key-here"
    access_token_expire_minutes: int = 30
    
    # WebSocket配置
    ws_heartbeat_interval: int = 30
    ws_max_connections: int = 100
    
    # 机器狗控制配置
    robot_command_timeout: float = 5.0
    robot_connection_retry: int = 3
    robot_heartbeat_interval: float = 10.0
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False


# 全局配置实例
settings = Settings()


def get_settings() -> Settings:
    """获取配置实例"""
    return settings
