"""
连接管理API测试
"""
import pytest
import asyncio
from unittest.mock import Mock, patch, AsyncMock
from fastapi.testclient import TestClient
import sys
import os

# 添加项目路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), ".."))
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "../../python"))

from app.main import app
from app.services.connection_manager import Go2ConnectionManager


class TestConnectionAPI:
    """连接API测试类"""
    
    def setup_method(self):
        """测试前设置"""
        self.client = TestClient(app)
    
    def test_health_check(self):
        """测试健康检查端点"""
        response = self.client.get("/health")
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "healthy"
        assert "service" in data
        assert "version" in data
    
    def test_root_endpoint(self):
        """测试根端点"""
        response = self.client.get("/")
        assert response.status_code == 200
        data = response.json()
        assert "message" in data
        assert "docs" in data
        assert "version" in data
    
    @patch('app.services.connection_manager.Go2ConnectionManager.connect')
    def test_connect_success(self, mock_connect):
        """测试成功连接"""
        # 模拟成功连接
        mock_connect.return_value = asyncio.Future()
        mock_connect.return_value.set_result(True)
        
        response = self.client.post("/api/connect", json={
            "robot_ip": "************",
            "token": "test_token"
        })
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["connected"] is True
        assert "message" in data
    
    @patch('app.services.connection_manager.Go2ConnectionManager.connect')
    def test_connect_failure(self, mock_connect):
        """测试连接失败"""
        # 模拟连接失败
        mock_connect.return_value = asyncio.Future()
        mock_connect.return_value.set_result(False)
        
        response = self.client.post("/api/connect", json={
            "robot_ip": "*************",
            "token": "test_token"
        })
        
        assert response.status_code == 400
    
    def test_connect_invalid_data(self):
        """测试无效连接数据"""
        response = self.client.post("/api/connect", json={
            "invalid_field": "value"
        })
        
        assert response.status_code == 422  # 数据验证错误
    
    @patch('app.services.connection_manager.Go2ConnectionManager.disconnect')
    def test_disconnect_success(self, mock_disconnect):
        """测试成功断开连接"""
        mock_disconnect.return_value = asyncio.Future()
        mock_disconnect.return_value.set_result(True)
        
        response = self.client.delete("/api/disconnect")
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
    
    @patch('app.services.connection_manager.Go2ConnectionManager.get_status')
    def test_get_status(self, mock_get_status):
        """测试获取连接状态"""
        # 模拟状态数据
        mock_status = {
            "connected": True,
            "last_heartbeat": "2024-12-27T10:00:00",
            "connection_info": {"ip": "*************"},
            "cached_data": {}
        }
        mock_get_status.return_value = mock_status
        
        response = self.client.get("/api/status")
        
        assert response.status_code == 200
        data = response.json()
        assert data["connected"] is True
        assert "last_heartbeat" in data
        assert "connection_info" in data
    
    def test_get_system_info(self):
        """测试获取系统信息"""
        response = self.client.get("/api/info")
        
        assert response.status_code == 200
        data = response.json()
        assert "api_version" in data
        assert "connections" in data
    
    def test_get_config(self):
        """测试获取配置信息"""
        response = self.client.get("/api/config")
        
        assert response.status_code == 200
        data = response.json()
        assert "robot_ip" in data
        assert "heartbeat_interval" in data
        assert "command_timeout" in data
    
    @patch('app.services.connection_manager.Go2ConnectionManager.disconnect')
    @patch('app.services.connection_manager.Go2ConnectionManager.connect')
    def test_reconnect_success(self, mock_connect, mock_disconnect):
        """测试重新连接成功"""
        mock_disconnect.return_value = asyncio.Future()
        mock_disconnect.return_value.set_result(True)
        mock_connect.return_value = asyncio.Future()
        mock_connect.return_value.set_result(True)
        
        response = self.client.post("/api/reconnect")
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["connected"] is True


@pytest.mark.asyncio
class TestConnectionManager:
    """连接管理器测试类"""
    
    def setup_method(self):
        """测试前设置"""
        self.manager = Go2ConnectionManager()
    
    def test_initial_state(self):
        """测试初始状态"""
        assert not self.manager.is_connected
        assert self.manager._connection is None
        assert len(self.manager._message_callbacks) == 0
    
    @patch('go2_webrtc.Go2Connection')
    async def test_connect_success(self, mock_go2_connection):
        """测试连接成功"""
        # 模拟Go2Connection
        mock_conn = Mock()
        mock_conn.validation_result = "SUCCESS"
        mock_conn.connect_robot = AsyncMock()
        mock_go2_connection.return_value = mock_conn
        
        result = await self.manager.connect("*************", "test_token")
        
        assert result is True
        assert self.manager.is_connected
    
    @patch('go2_webrtc.Go2Connection')
    async def test_connect_failure(self, mock_go2_connection):
        """测试连接失败"""
        # 模拟连接失败
        mock_conn = Mock()
        mock_conn.validation_result = "FAILED"
        mock_conn.connect_robot = AsyncMock()
        mock_go2_connection.return_value = mock_conn
        
        result = await self.manager.connect("*************", "test_token")
        
        assert result is False
        assert not self.manager.is_connected
    
    async def test_disconnect(self):
        """测试断开连接"""
        # 模拟已连接状态
        mock_conn = Mock()
        mock_conn.pc.close = AsyncMock()
        self.manager._connection = mock_conn
        self.manager._is_connected = True
        
        result = await self.manager.disconnect()
        
        assert result is True
        assert not self.manager.is_connected
        assert self.manager._connection is None
    
    def test_get_status(self):
        """测试获取状态"""
        status = self.manager.get_status()
        
        assert "connected" in status
        assert "last_heartbeat" in status
        assert "cached_data" in status
        assert "connection_info" in status
    
    def test_message_callbacks(self):
        """测试消息回调管理"""
        callback = Mock()
        
        # 添加回调
        self.manager.add_message_callback(callback)
        assert callback in self.manager._message_callbacks
        
        # 移除回调
        self.manager.remove_message_callback(callback)
        assert callback not in self.manager._message_callbacks
    
    @patch('go2_webrtc.ROBOT_CMD', {'TestCommand': 1001})
    async def test_send_command_success(self):
        """测试发送命令成功"""
        # 模拟已连接状态
        mock_data_channel = Mock()
        mock_data_channel.send = Mock()
        
        mock_conn = Mock()
        mock_conn.data_channel = mock_data_channel
        
        self.manager._connection = mock_conn
        self.manager._is_connected = True
        
        result = await self.manager.send_command("TestCommand", {"param": "value"})
        
        assert result is True
        mock_data_channel.send.assert_called_once()
    
    async def test_send_command_not_connected(self):
        """测试未连接时发送命令"""
        result = await self.manager.send_command("TestCommand")
        
        assert result is False
    
    async def test_send_command_unknown(self):
        """测试发送未知命令"""
        # 模拟已连接状态
        self.manager._is_connected = True
        
        result = await self.manager.send_command("UnknownCommand")
        
        assert result is False


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
