#!/usr/bin/env python3
"""
Go2 WebRTC API 服务器启动脚本
独立启动后端API服务器
"""
import os
import sys
import asyncio
import argparse
import signal
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root.parent / "python"))

# 导入应用模块
try:
    import uvicorn
    from app.main import app
    from app.core.config import get_settings
    from app.core.logging import setup_logging, get_logger
    from app.services.state_manager import get_state_manager
except ImportError as e:
    print(f"导入错误: {e}")
    print("请确保已安装所有依赖: pip install -r requirements.txt")
    sys.exit(1)


def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(
        description="Go2 WebRTC API 服务器",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  python start_server.py                    # 使用默认配置启动
  python start_server.py --host 0.0.0.0    # 指定监听地址
  python start_server.py --port 8080       # 指定端口
  python start_server.py --reload          # 开发模式（自动重载）
  python start_server.py --log-level DEBUG # 设置日志级别
        """
    )
    
    parser.add_argument(
        "--host",
        default=None,
        help="服务器监听地址 (默认: 从配置文件读取)"
    )
    
    parser.add_argument(
        "--port",
        type=int,
        default=None,
        help="服务器监听端口 (默认: 从配置文件读取)"
    )
    
    parser.add_argument(
        "--reload",
        action="store_true",
        help="启用自动重载 (开发模式)"
    )
    
    parser.add_argument(
        "--log-level",
        choices=["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"],
        default=None,
        help="日志级别 (默认: 从配置文件读取)"
    )
    
    parser.add_argument(
        "--workers",
        type=int,
        default=1,
        help="工作进程数量 (默认: 1)"
    )
    
    parser.add_argument(
        "--config",
        help="配置文件路径 (默认: .env)"
    )
    
    parser.add_argument(
        "--robot-ip",
        help="机器狗IP地址 (覆盖配置文件设置)"
    )
    
    parser.add_argument(
        "--robot-token",
        help="机器狗认证令牌 (覆盖配置文件设置)"
    )
    
    return parser.parse_args()


def setup_environment(args):
    """设置环境变量"""
    if args.config:
        os.environ["ENV_FILE"] = args.config
    
    if args.log_level:
        os.environ["LOG_LEVEL"] = args.log_level
    
    if args.robot_ip:
        os.environ["GO2_IP"] = args.robot_ip
    
    if args.robot_token:
        os.environ["GO2_TOKEN"] = args.robot_token


def check_dependencies():
    """检查依赖项"""
    required_modules = [
        "fastapi", "uvicorn", "websockets", "pydantic", 
        "aiortc", "aiohttp", "requests"
    ]
    
    missing_modules = []
    for module in required_modules:
        try:
            __import__(module)
        except ImportError:
            missing_modules.append(module)
    
    if missing_modules:
        print("缺少以下依赖模块:")
        for module in missing_modules:
            print(f"  - {module}")
        print("\n请运行以下命令安装依赖:")
        print("pip install -r requirements.txt")
        return False
    
    return True


def print_startup_info(settings, host, port):
    """打印启动信息"""
    print("=" * 60)
    print("Go2 WebRTC API 服务器")
    print("=" * 60)
    print(f"服务器地址: http://{host}:{port}")
    print(f"API文档: http://{host}:{port}/docs")
    print(f"ReDoc文档: http://{host}:{port}/redoc")
    print(f"健康检查: http://{host}:{port}/health")
    print("-" * 60)
    print(f"机器狗IP: {settings.go2_ip}")
    print(f"日志级别: {settings.log_level}")
    print(f"WebSocket端点: ws://{host}:{port}/ws/robot")
    print("=" * 60)
    print("按 Ctrl+C 停止服务器")
    print()


async def startup_tasks():
    """启动任务"""
    logger = get_logger("startup")
    
    try:
        # 启动状态管理器
        state_manager = get_state_manager()
        await state_manager.start()
        logger.info("状态管理器启动成功")
        
        # 其他启动任务可以在这里添加
        
    except Exception as e:
        logger.error("启动任务执行失败", error=str(e))
        raise


async def shutdown_tasks():
    """关闭任务"""
    logger = get_logger("shutdown")
    
    try:
        # 停止状态管理器
        state_manager = get_state_manager()
        await state_manager.stop()
        logger.info("状态管理器已停止")
        
        # 其他清理任务可以在这里添加
        
    except Exception as e:
        logger.error("关闭任务执行失败", error=str(e))


def signal_handler(signum, frame):
    """信号处理器"""
    logger = get_logger("signal")
    logger.info(f"收到信号 {signum}，正在关闭服务器...")
    
    # 执行清理任务
    try:
        asyncio.run(shutdown_tasks())
    except Exception as e:
        logger.error("清理任务执行失败", error=str(e))
    
    sys.exit(0)


def main():
    """主函数"""
    # 解析命令行参数
    args = parse_arguments()
    
    # 设置环境变量
    setup_environment(args)
    
    # 检查依赖项
    if not check_dependencies():
        sys.exit(1)
    
    # 设置日志
    setup_logging()
    logger = get_logger("main")
    
    # 获取配置
    settings = get_settings()
    
    # 确定服务器配置
    host = args.host or settings.api_host
    port = args.port or settings.api_port
    reload = args.reload or settings.api_reload
    
    # 打印启动信息
    print_startup_info(settings, host, port)
    
    # 注册信号处理器
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    try:
        # 执行启动任务
        asyncio.run(startup_tasks())
        
        # 启动服务器
        logger.info("启动Go2 WebRTC API服务器", host=host, port=port)
        
        uvicorn.run(
            "app.main:app",
            host=host,
            port=port,
            reload=reload,
            workers=args.workers if not reload else 1,
            log_level=settings.log_level.lower(),
            access_log=True,
            server_header=False,
            date_header=False
        )
        
    except KeyboardInterrupt:
        logger.info("用户中断，正在关闭服务器...")
    except Exception as e:
        logger.critical("服务器启动失败", error=str(e))
        sys.exit(1)
    finally:
        # 执行清理任务
        try:
            asyncio.run(shutdown_tasks())
        except Exception as e:
            logger.error("清理任务执行失败", error=str(e))


if __name__ == "__main__":
    main()
