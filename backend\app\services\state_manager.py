"""
状态管理和缓存服务
处理机器狗状态数据的缓存、更新和广播
"""
import asyncio
import json
import logging
from typing import Dict, Any, Optional, List, Callable
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
import threading

logger = logging.getLogger(__name__)


@dataclass
class StateData:
    """状态数据结构"""
    topic: str
    data: Dict[str, Any]
    timestamp: datetime
    message_type: str = "unknown"
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "topic": self.topic,
            "data": self.data,
            "timestamp": self.timestamp.isoformat(),
            "message_type": self.message_type
        }


class StateCache:
    """状态缓存管理器"""
    
    def __init__(self, max_history: int = 100, ttl_seconds: int = 300):
        self.max_history = max_history
        self.ttl_seconds = ttl_seconds
        self._cache: Dict[str, List[StateData]] = {}
        self._lock = threading.RLock()
        self._subscribers: List[Callable[[StateData], None]] = []
        
    def update(self, topic: str, data: Dict[str, Any], message_type: str = "unknown"):
        """更新状态数据"""
        with self._lock:
            state_data = StateData(
                topic=topic,
                data=data,
                timestamp=datetime.now(),
                message_type=message_type
            )
            
            # 初始化主题缓存
            if topic not in self._cache:
                self._cache[topic] = []
            
            # 添加新数据
            self._cache[topic].append(state_data)
            
            # 限制历史记录数量
            if len(self._cache[topic]) > self.max_history:
                self._cache[topic] = self._cache[topic][-self.max_history:]
            
            # 通知订阅者
            self._notify_subscribers(state_data)
            
            logger.debug(f"状态更新: {topic} - {len(self._cache[topic])} 条记录")
    
    def get_latest(self, topic: str) -> Optional[StateData]:
        """获取最新状态"""
        with self._lock:
            if topic in self._cache and self._cache[topic]:
                return self._cache[topic][-1]
            return None
    
    def get_history(self, topic: str, limit: int = 10) -> List[StateData]:
        """获取历史状态"""
        with self._lock:
            if topic in self._cache:
                return self._cache[topic][-limit:]
            return []
    
    def get_all_latest(self) -> Dict[str, StateData]:
        """获取所有主题的最新状态"""
        with self._lock:
            result = {}
            for topic, states in self._cache.items():
                if states:
                    result[topic] = states[-1]
            return result
    
    def cleanup_expired(self):
        """清理过期数据"""
        with self._lock:
            cutoff_time = datetime.now() - timedelta(seconds=self.ttl_seconds)
            
            for topic in list(self._cache.keys()):
                # 过滤掉过期数据
                self._cache[topic] = [
                    state for state in self._cache[topic]
                    if state.timestamp > cutoff_time
                ]
                
                # 如果主题没有数据了，删除主题
                if not self._cache[topic]:
                    del self._cache[topic]
    
    def subscribe(self, callback: Callable[[StateData], None]):
        """订阅状态更新"""
        self._subscribers.append(callback)
    
    def unsubscribe(self, callback: Callable[[StateData], None]):
        """取消订阅"""
        if callback in self._subscribers:
            self._subscribers.remove(callback)
    
    def _notify_subscribers(self, state_data: StateData):
        """通知订阅者"""
        for callback in self._subscribers:
            try:
                callback(state_data)
            except Exception as e:
                logger.error(f"状态订阅回调执行失败: {e}")
    
    def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        with self._lock:
            return {
                "topics_count": len(self._cache),
                "total_records": sum(len(states) for states in self._cache.values()),
                "subscribers_count": len(self._subscribers),
                "topics": {
                    topic: {
                        "records_count": len(states),
                        "latest_timestamp": states[-1].timestamp.isoformat() if states else None
                    }
                    for topic, states in self._cache.items()
                }
            }


class StateBroadcaster:
    """状态广播器"""
    
    def __init__(self):
        self._websocket_callbacks: List[Callable[[Dict[str, Any]], None]] = []
        self._http_callbacks: List[Callable[[Dict[str, Any]], None]] = []
        
    def add_websocket_callback(self, callback: Callable[[Dict[str, Any]], None]):
        """添加WebSocket广播回调"""
        self._websocket_callbacks.append(callback)
    
    def remove_websocket_callback(self, callback: Callable[[Dict[str, Any]], None]):
        """移除WebSocket广播回调"""
        if callback in self._websocket_callbacks:
            self._websocket_callbacks.remove(callback)
    
    def add_http_callback(self, callback: Callable[[Dict[str, Any]], None]):
        """添加HTTP广播回调"""
        self._http_callbacks.append(callback)
    
    def remove_http_callback(self, callback: Callable[[Dict[str, Any]], None]):
        """移除HTTP广播回调"""
        if callback in self._http_callbacks:
            self._http_callbacks.remove(callback)
    
    async def broadcast_websocket(self, message: Dict[str, Any]):
        """广播WebSocket消息"""
        for callback in self._websocket_callbacks:
            try:
                if asyncio.iscoroutinefunction(callback):
                    await callback(message)
                else:
                    callback(message)
            except Exception as e:
                logger.error(f"WebSocket广播回调执行失败: {e}")
    
    async def broadcast_http(self, message: Dict[str, Any]):
        """广播HTTP消息"""
        for callback in self._http_callbacks:
            try:
                if asyncio.iscoroutinefunction(callback):
                    await callback(message)
                else:
                    callback(message)
            except Exception as e:
                logger.error(f"HTTP广播回调执行失败: {e}")


class StateManager:
    """状态管理器主类"""
    
    def __init__(self, max_history: int = 100, ttl_seconds: int = 300):
        self.cache = StateCache(max_history, ttl_seconds)
        self.broadcaster = StateBroadcaster()
        self._cleanup_task: Optional[asyncio.Task] = None
        self._running = False
        
        # 订阅缓存更新，自动广播
        self.cache.subscribe(self._on_state_update)
    
    async def start(self):
        """启动状态管理器"""
        if self._running:
            return
        
        self._running = True
        # 启动清理任务
        self._cleanup_task = asyncio.create_task(self._cleanup_loop())
        logger.info("状态管理器已启动")
    
    async def stop(self):
        """停止状态管理器"""
        self._running = False
        
        if self._cleanup_task:
            self._cleanup_task.cancel()
            try:
                await self._cleanup_task
            except asyncio.CancelledError:
                pass
        
        logger.info("状态管理器已停止")
    
    def _on_state_update(self, state_data: StateData):
        """状态更新回调"""
        # 创建广播消息
        broadcast_message = {
            "type": "state_update",
            "data": state_data.to_dict(),
            "timestamp": datetime.now().isoformat()
        }
        
        # 异步广播
        asyncio.create_task(self.broadcaster.broadcast_websocket(broadcast_message))
    
    async def _cleanup_loop(self):
        """清理循环"""
        while self._running:
            try:
                await asyncio.sleep(60)  # 每分钟清理一次
                self.cache.cleanup_expired()
                logger.debug("状态缓存清理完成")
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"状态缓存清理失败: {e}")
    
    def update_state(self, topic: str, data: Dict[str, Any], message_type: str = "unknown"):
        """更新状态"""
        self.cache.update(topic, data, message_type)
    
    def get_current_state(self) -> Dict[str, Any]:
        """获取当前完整状态"""
        all_latest = self.cache.get_all_latest()
        return {
            topic: state.to_dict()
            for topic, state in all_latest.items()
        }
    
    def get_topic_state(self, topic: str) -> Optional[Dict[str, Any]]:
        """获取特定主题状态"""
        latest = self.cache.get_latest(topic)
        return latest.to_dict() if latest else None
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        return self.cache.get_stats()


# 全局状态管理器实例
state_manager = StateManager()


def get_state_manager() -> StateManager:
    """获取状态管理器实例"""
    return state_manager
